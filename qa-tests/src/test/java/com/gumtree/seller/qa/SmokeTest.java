package com.gumtree.seller.qa;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.flogger.Flogger;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.net.SocketTimeoutException;
import java.util.Map;

import static java.util.concurrent.TimeUnit.SECONDS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

@Flogger
class SmokeTest extends BaseTest {

    @Test
    @DisplayName("all dependencies return successfully")
    void dependenciesOk() {
        await().atMost(60, SECONDS).pollInterval(1, SECONDS)
                .alias("wait until all dependencies are green")
                .until(() -> {
                    try {
                        var dependencyToStatus = sellerClient.get("/admin-api/dependencies", new TypeReference<Map<String, Boolean>>() {
                        });

                        var dependencies = dependencyToStatus.keySet();
                        assertThat(dependencies).containsExactlyInAnyOrder(
                                "bapi",
                                "livead-search",
                                "fullad-search-service",
                                "locations",
                                "user-service",
                                "media-processor",
                                "draft-ads-api",
                                "motors-price-guidance",
                                "motors-api",
                                "user-reviews",
                                "cv-store",
                                "ad-counters-api",
                                "redis"
                        );

                        var failingDependencies = dependencies.stream()
                                .filter(name -> !dependencyToStatus.get(name))
                                .toList();

                        log.atInfo().log("failing dependencies: %s", failingDependencies);

                        return failingDependencies.isEmpty();
                    } catch (SocketTimeoutException exc) {
                        return false;
                    }
                });
    }
}
