<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.gumtree.web.seller</groupId>
  <artifactId>google-analytics</artifactId>
  <name>Google Analytics</name>

  <parent>
    <groupId>com.gumtree.web</groupId>
    <artifactId>seller</artifactId>
    <version>3.0-SNAPSHOT</version>
  </parent>

  <properties>
    <jib.skip>true</jib.skip>
    <checkstyle.maxviolations>1</checkstyle.maxviolations>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.gumtree.shared-commons.properties</groupId>
      <artifactId>gtprops-spring</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>web-common</artifactId>
      <exclusions>
        <exclusion>
          <groupId>junit</groupId>
          <artifactId>junit-dep</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.hibernate</groupId>
          <artifactId>hibernate-validator</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>httpcore</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>httpclient</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ning</groupId>
      <artifactId>async-http-client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.easytesting</groupId>
      <artifactId>fest-assert-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.bigtesting</groupId>
      <artifactId>fixd</artifactId>
      <version>1.0.3</version>
    </dependency>
  </dependencies>
</project>
