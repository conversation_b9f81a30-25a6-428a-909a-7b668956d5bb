package com.gumtree.web.security;

import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.userapi.model.GumtreeAccessToken;
import com.gumtree.web.security.shiro.RememberMeJsonSerializer;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.MockClock;
import io.micrometer.core.instrument.simple.SimpleConfig;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.apache.commons.codec.binary.Base64;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.crypto.AesCipherService;
import org.apache.shiro.crypto.CipherService;
import org.apache.shiro.mgt.RememberMeManager;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.mgt.CookieRememberMeManager;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.mgt.RotatingKeyCookieRememberMeManager;
import org.apache.shiro.web.servlet.Cookie;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import static com.gumtree.metrics.AuthenticationMetrics.ACTION;
import static com.gumtree.metrics.AuthenticationMetrics.Action;
import static com.gumtree.metrics.AuthenticationMetrics.NAME;
import static com.gumtree.metrics.AuthenticationMetrics.STATUS;
import static com.gumtree.metrics.AuthenticationMetrics.Status;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SecurityHelperTest {

    private static final String REALM = "gumtree";

    @Mock
    private UserServiceFacade userServiceFacade;

    @Mock
    private Subject subject;

    private SecurityHelper securityHelper;

    private MeterRegistry meterRegistry;
    private String encryptionKey = "pbfO1tAjR1zDyxG2cKDz9g==";
    private String previousEncryptionKey = "ukNHChzQjTL5qJNeqN5azQ==";
    private String nextEncryptionKey = "GaJ5qv6tdSQhuRJOzpwYMA==";

    @Spy
    org.apache.shiro.mgt.SecurityManager securityManager = securityManager();

    @Before
    public void init(){

        SecurityUtils.setSecurityManager(securityManager);

        meterRegistry = new SimpleMeterRegistry(SimpleConfig.DEFAULT, new MockClock());
        securityHelper = new SecurityHelper(userServiceFacade, meterRegistry);

        GumtreeAccessToken accessToken = GumtreeAccessToken.create(3L);
        SimplePrincipalCollection principals = new SimplePrincipalCollection("<EMAIL>", REALM);
        principals.add(accessToken, REALM);

        when(subject.getPrincipals()).thenReturn(principals);
        doNothing().when(subject).logout();
    }

    @Test
    public void testVerifyAccessTokenPositive(){

        when(userServiceFacade.verifyAccessToken(any())).thenReturn(ApiResponse.of(true));

        boolean isValid = securityHelper.verifyAccessTokenAndLogoutIfInvalid(subject);
        assertMetricValue(1, Action.ACCESS_TOKEN, Status.SUCCESS);

        assertTrue(isValid);
    }

    @Test
    public void testVerifyAccessTokenNegative(){

        when(userServiceFacade.verifyAccessToken(any())).thenReturn(ApiResponse.of(false));

        boolean isValid = securityHelper.verifyAccessTokenAndLogoutIfInvalid(subject);
        verify(subject, times(1)).logout();
        assertMetricValue(1, Action.ACCESS_TOKEN, Status.FAILURE);

        assertFalse(isValid);
    }

    private void assertMetricValue(int expectedValue, Action action, Status status) {
        Counter counter = meterRegistry.counter(NAME, ACTION, action.toString(), STATUS, status.toString());
        assertEquals(expectedValue, counter.count(), 0.1D);
    }

    private org.apache.shiro.mgt.SecurityManager securityManager() {
        DefaultWebSecurityManager manager = new DefaultWebSecurityManager();
        manager.setRememberMeManager(rememberMeManager());
        return manager;
    }

    private CipherService cipherService() {
        // we need a 128bit encryption suite for decrypting/encrypting cookie
        AesCipherService cipherService = new AesCipherService();
        cipherService.setKeySize(128);
        cipherService.setInitializationVectorSize(128);

        return cipherService;
    }

    private SimpleCookie rememberMeCookieTemplate() {
        final SimpleCookie cookieTemplate =
                new SimpleCookie("gt_rememberMe");
        cookieTemplate.setMaxAge(Cookie.ONE_YEAR);
        cookieTemplate.setHttpOnly(true);
        cookieTemplate.setSecure(false);
        cookieTemplate.setDomain("dev.gumtree.com");

        return cookieTemplate;
    }


    private RememberMeManager rememberMeManager() {

        final CookieRememberMeManager rememberMeManager = new RotatingKeyCookieRememberMeManager(
                Base64.decodeBase64(encryptionKey),
                Base64.decodeBase64(nextEncryptionKey),
                Base64.decodeBase64(previousEncryptionKey),
                meterRegistry);
        rememberMeManager.setCipherService(cipherService());
        rememberMeManager.setCookie(rememberMeCookieTemplate());
        rememberMeManager.setSerializer(new RememberMeJsonSerializer());

        return rememberMeManager;
    }

}
