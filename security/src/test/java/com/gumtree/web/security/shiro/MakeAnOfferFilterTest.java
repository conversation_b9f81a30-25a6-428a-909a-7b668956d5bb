package com.gumtree.web.security.shiro;

import com.netflix.config.ConfigurationManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;

import java.util.Optional;
import java.util.Properties;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MakeAnOfferFilterTest {

    @Mock
    private HttpServletRequest httpRequest;

    private void loadProperties() {
        Properties properties = new Properties();
        properties.setProperty("gumtree.url.buyer.base_uri", "");
        properties.setProperty("gumtree.url.seller.base_uri", "");
        properties.setProperty("gumtree.url.seller.secure.base_uri", "");
        properties.setProperty("gumtree.url.reply.base_uri", "");
        properties.setProperty("gumtree.url.ok_ai.base_uri", "");
        ConfigurationManager.loadProperties(properties);
    }

    @Before
    public void init() {
        loadProperties();
    }

    @Test
    public void shouldBuildCallbackUrlIfRefererIsValid() {
        // given a request with valid Referer header
        when(httpRequest.getHeader("Referer")).thenReturn("http://www.gumtree.com/p/123");

        // when
        assertThat(MakeAnOfferFilter.buildCallbackURL(httpRequest))
                .isEqualTo(Optional.of("http://www.gumtree.com/p/123?lcb=true"));
    }

    @Test
    public void shouldBuildCallbackUrlIfRefererIsValidAndAlreadyHasQueryParams() {
        // given a request with valid Referer header
        when(httpRequest.getHeader("Referer")).thenReturn("http://www.gumtree.com/p/123?test=1");

        // when
        assertThat(MakeAnOfferFilter.buildCallbackURL(httpRequest))
                .isEqualTo(Optional.of("http://www.gumtree.com/p/123?test=1&lcb=true"));
    }

    @Test
    public void shouldBuildCallbackUrlIfRefererIsValidAndAlreadyHasLoginCallbackQueryParams() {
        // given a request with valid Referer header
        when(httpRequest.getHeader("Referer")).thenReturn("http://www.gumtree.com/p/123?lcb=true");

        // when
        assertThat(MakeAnOfferFilter.buildCallbackURL(httpRequest))
                .isEqualTo(Optional.of("http://www.gumtree.com/p/123?lcb=true"));
    }

    @Test
    public void shouldNotBuildCallbackUrlIfRefererIsNotValid() {
        // given a request with valid Referer header
        when(httpRequest.getHeader("Referer")).thenReturn("non valid");

        // when
        assertThat(MakeAnOfferFilter.buildCallbackURL(httpRequest))
                .isEqualTo(Optional.empty());
    }
}