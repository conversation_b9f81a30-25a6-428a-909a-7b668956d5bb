package com.gumtree.web.security.shiro;

import com.gumtree.web.cookie.CookieResolver;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.*;

/**
 */
public class ShiroLoginUtilsTest extends BaseShiroTest {

    private Subject subject;

    private Session session;
    private CookieResolver cookieResolver;

    @Before
    public void init() {
        subject = mock(Subject.class);
        session = mock(Session.class);
        cookieResolver = mock(CookieResolver.class);
        setSubject(subject);
        when(subject.getSession()).thenReturn(session);
    }

    @Test
    public void userIsNotDirtyWhenNoAttributeInSession() {
        assertThat(new ShiroLoginUtils(cookieResolver).isUserDirty(), equalTo(false));
    }

    @Test
    public void userIsDirtyWhenSessionAttributeIsTrue() {
        when(session.getAttribute(ShiroLoginUtils.USER_IS_DIRTY)).thenReturn(Boolean.TRUE);
        assertThat(new ShiroLoginUtils(cookieResolver).isUserDirty(), equalTo(true));
    }

    @Test
    public void userIsNoDirtyWhenSessionAttributeIsFalse() {
        when(session.getAttribute(ShiroLoginUtils.USER_IS_DIRTY)).thenReturn(Boolean.FALSE);
        assertThat(new ShiroLoginUtils(cookieResolver).isUserDirty(), equalTo(false));
    }

    @Test
    public void newUserMustLoginToStartPostAdFlowWhenNoAttributeInSession() {
        assertThat(new ShiroLoginUtils(cookieResolver)
                .newUserMustLoginToStartPostAdFlow(), equalTo(true));
    }

    @Test
    public void newUserDoesNotNeedToLoginToStartPostAdFlowWhenSessionAttributeIsFalse() {
        when(session.getAttribute(ShiroLoginUtils.POST_AD_LOGIN_FLAG)).thenReturn(Boolean.FALSE);
        assertThat(new ShiroLoginUtils(cookieResolver)
                .newUserMustLoginToStartPostAdFlow(), equalTo(false));
    }

    @Test
    public void newUserMustLoginToStartPostAdFlowWhenSessionAttributeIsTrue() {
        when(session.getAttribute(ShiroLoginUtils.POST_AD_LOGIN_FLAG)).thenReturn(Boolean.TRUE);
        assertThat(new ShiroLoginUtils(cookieResolver)
                .newUserMustLoginToStartPostAdFlow(), equalTo(true));
    }

    @Test
    public void setNewUserMustLoginToStartPostAdFlowRemovesSessionAttributeWhenFlagIsTrue() {
        new ShiroLoginUtils(cookieResolver)
                .setNewUserMustLoginToStartPostAdFlow(true);
        verify(session).removeAttribute(ShiroLoginUtils.POST_AD_LOGIN_FLAG);
    }

    @Test
    public void setNewUserMustLoginToStartPostAdFlowAddsSessionAttributeWhenFlagIsFalse() {
        new ShiroLoginUtils(cookieResolver)
                .setNewUserMustLoginToStartPostAdFlow(false);
        verify(session).setAttribute(ShiroLoginUtils.POST_AD_LOGIN_FLAG, Boolean.FALSE);
    }

    @Test
    public void storeNewUserEmailAddressInSessionAddsEmailToSession() {
        new ShiroLoginUtils(cookieResolver)
                .storeNewUserEmailAddressInSession("<EMAIL>");
        verify(session).setAttribute(ShiroLoginUtils.NEW_USER_EMAIL_ADDRESS, "<EMAIL>");
    }

    @Test
    public void getNewUserEmailAddressFromSessionReturnsEmailAddressFromSession() {
        when(session.getAttribute(ShiroLoginUtils.NEW_USER_EMAIL_ADDRESS)).thenReturn("<EMAIL>");
        String emailAddress = new ShiroLoginUtils(cookieResolver)
                .getNewUserEmailAddressFromSession();
        assertThat(emailAddress, equalTo("<EMAIL>"));
    }

    @Test
    public void clearNewUserEmailAddressFromSessionRemovesEmailFromSession() {
        new ShiroLoginUtils(cookieResolver)
                .clearNewUserEmailAddressFromSession();
        verify(session).removeAttribute(ShiroLoginUtils.NEW_USER_EMAIL_ADDRESS);
    }
}
