package org.apache.shiro.web.mgt;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.gumtree.metrics.AuthenticationMetrics;
import com.gumtree.userapi.model.GumtreeAccessToken;
import com.gumtree.web.security.shiro.GumtreeAuthenticationToken;
import com.gumtree.web.security.shiro.RememberMeJsonSerializer;
import com.gumtree.web.security.shiro.UserAuthenticationInfo;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.MockClock;
import io.micrometer.core.instrument.simple.SimpleConfig;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.apache.shiro.crypto.AesCipherService;
import org.apache.shiro.io.Serializer;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.web.servlet.ShiroHttpServletRequest;
import org.apache.shiro.web.subject.WebSubjectContext;
import org.apache.shiro.web.subject.support.DefaultWebSubjectContext;
import org.apache.shiro.web.subject.support.WebDelegatingSubject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

import static com.gumtree.metrics.AuthenticationMetrics.ACTION;
import static com.gumtree.metrics.AuthenticationMetrics.NAME;
import static com.gumtree.metrics.AuthenticationMetrics.STATUS;
import static com.gumtree.metrics.AuthenticationMetrics.Status;
import static java.util.Base64.getDecoder;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.closeTo;
import static org.hamcrest.Matchers.hasItem;
import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RotatingKeyCookieRememberMeManagerTest {

    public static final int KEY_SIZE = 128;

    public static final String CONTEXT_PATH = "my.gumtree.com";
    public static final String SHIRO_REMEMBER_ME_COOKIE_NAME = "rememberMe";
    public static final String HTTP_HEADER_SET_COOKIE = "Set-Cookie";

    public static final String JSON_FIELD_PRINCIPAL = "principal";
    public static final String JSON_FIELD_REALM = "realm";
    public static final String JSON_FIELD_TOKEN = "token";

    private static class PrincipalPOJO {

        public PrincipalPOJO(String principal, String realm, String token){
            this.principal = principal;
            this.realm = realm;
            this.token = token;
        }

        public String principal;
        public String realm;
        public String token;
    }

    private static final String PRINCIPAL_USERNAME = "<EMAIL>";
    private static final String PRINCIPAL_REALM = "gumtree";
    private static final String PRINCIPAL_VALUE = "3";

    private static final byte[] NEXT = getDecoder().decode("xNiTYBskd3HKCFUWjznFJw==".getBytes());
    private static final byte[] PREVIOUS = getDecoder().decode("FvXSrw/oyBd9hYwrwxgdTA==".getBytes());
    private static final byte[] CURRENT = getDecoder().decode("+Q4wlIMGcDhuqIg/BVFS7g==".getBytes());

    public RotatingKeyCookieRememberMeManagerTest(){
        jsonSerializer = new RememberMeJsonSerializer();
        cipherService = new AesCipherService();
        cipherService.setKeySize(KEY_SIZE);
        cipherService.setInitializationVectorSize(KEY_SIZE);
    }

    @Mock
    protected HttpServletRequest request;

    @Mock
    protected HttpServletResponse response;

    @Mock
    protected WebDelegatingSubject webDelegatingSubject;

    @Mock
    protected WebSubjectContext webSubjectContext;

    private RememberMeJsonSerializer jsonSerializer;
    private AesCipherService cipherService;
    private MeterRegistry meterRegistry;
    protected Cookie rememberMeCookie;

    @Before
    public void before(){
        // Initialize the Promteheus metric registry
        meterRegistry = new SimpleMeterRegistry(SimpleConfig.DEFAULT, new MockClock());
    }

    @Test
    public void shouldTryAllKeys() {

        // A few important observations:
        // 1. The cipher can create different encrypted values for the same value using the same key
        // 2. It is possible for two different keys to decrypt the same encrypted value but with different decrypted results
        // 3. Observation 2 happens quite frequently, as frequent as once in 100 tries.

        // Based on the above, this test verifies that that the encryption and decryption mechanism works ie - that
        // the value encrypted using the next key is decrypted by the next key even if it is decrypted incorrectly by the current or previous keys before it.
        // Based on observations, several thousand tries should be enough to get at least once occurrence where the same value can be decrypted by both the previous
        // and the next keys.
        rememberMeCookie = new Cookie(SHIRO_REMEMBER_ME_COOKIE_NAME, "");
        for (int i = 0; i < 10000; i++) {
            final SimplePrincipalCollection principalCollection =
                    new SimplePrincipalCollection(ImmutableList.of(UUID.randomUUID().toString()), PRINCIPAL_REALM);

            final byte[] serialized = serialize(principalCollection);
            final PrincipalCollection deserialized = deserialize(serialized);

            assertEquals(principalCollection, deserialized);

            final RotatingKeyCookieRememberMeManager obj = new RotatingKeyCookieRememberMeManager(CURRENT, NEXT, PREVIOUS, meterRegistry);
            obj.setSerializer(new RememberMeJsonSerializer());
            final DefaultWebSubjectContext subjectContext = new DefaultWebSubjectContext();

            rememberMeCookie.setValue(new String(encrypt(serialized, NEXT)));
            when(request.getCookies()).thenReturn(new Cookie[]{rememberMeCookie});

            subjectContext.setServletRequest(request);
            subjectContext.setServletResponse(response);

            obj.getRememberedPrincipals(subjectContext);

        }

        assertRememberMeMetrics(Status.SUCCESS,10000);
        assertRememberMeMetrics(Status.FAILURE, 0);
    }

    @Test
    public void shouldSerializePrincipalsInJSONFormat() throws ClassNotFoundException, IOException {
      
        // Given
        RotatingKeyCookieRememberMeManager rememberMeMgr = createRememberMeManager(jsonSerializer);
        UserAuthenticationInfo authInfo = createAuthenticationInfoFixture(PRINCIPAL_USERNAME, PRINCIPAL_VALUE);
        ArgumentCaptor<String> httpHeader = initMocksForSerialization();

        // When
        rememberMeMgr.rememberIdentity(webDelegatingSubject, new GumtreeAuthenticationToken(), authInfo);

        // Then
        verify(response).addHeader(eq(HTTP_HEADER_SET_COOKIE), httpHeader.capture());
        byte[] json = decrypt(getRememberMeCookieFromHttpHeader(httpHeader.getValue()).getBytes(), CURRENT);

        assertThat(parseJson(json, JSON_FIELD_PRINCIPAL), is(PRINCIPAL_USERNAME));
        assertThat(parseJson(json, JSON_FIELD_REALM), is(PRINCIPAL_REALM));
        assertThat(parseJson(json, JSON_FIELD_TOKEN), is(PRINCIPAL_VALUE));
    }

    @Test
    public void shouldDeserializePrincipalsInJSONFormat() throws JsonProcessingException {

        // Given
        rememberMeCookie = new Cookie(SHIRO_REMEMBER_ME_COOKIE_NAME, "");
        RotatingKeyCookieRememberMeManager rememberMeManager = createRememberMeManager(jsonSerializer);
        initMocksForDeserialization();
        PrincipalPOJO principalsPojo = new PrincipalPOJO(PRINCIPAL_USERNAME, PRINCIPAL_REALM, PRINCIPAL_VALUE);

        byte[] serialized = new ObjectMapper().writeValueAsBytes(principalsPojo);
        rememberMeCookie.setValue(new String(encrypt(serialized, CURRENT)));

        // When
        PrincipalCollection principals = rememberMeManager.getRememberedPrincipals(webSubjectContext);

        // Then
        assertPrincipals(principals, PRINCIPAL_USERNAME, PRINCIPAL_REALM, PRINCIPAL_VALUE);
        assertRememberMeMetrics(Status.SUCCESS, 1);
    }

    @Test
    public void shouldDeserializePrincipalsInBinaryFormat() throws IOException {

        // Given
        rememberMeCookie = new Cookie(SHIRO_REMEMBER_ME_COOKIE_NAME, "");
        RotatingKeyCookieRememberMeManager rememberMeManager = createRememberMeManager(jsonSerializer);
        initMocksForDeserialization();

        byte[] serialized = serializePrincipalsIntoBinary(getPrincipalCollectionFixture(PRINCIPAL_USERNAME, PRINCIPAL_REALM, PRINCIPAL_VALUE));
        rememberMeCookie.setValue(new String(encrypt(serialized, CURRENT)));

        // When
        PrincipalCollection principals = rememberMeManager.getRememberedPrincipals(webSubjectContext);

        // Then
        assertPrincipals(principals, PRINCIPAL_USERNAME, PRINCIPAL_REALM, PRINCIPAL_VALUE);
        assertRememberMeMetrics(Status.SUCCESS, 1);
    }

    private byte[] serialize(PrincipalCollection principalCollection) {
        return jsonSerializer.serialize(principalCollection);
    }

    private PrincipalCollection deserialize(byte[] serialized) {
        return (PrincipalCollection) jsonSerializer.deserialize(serialized);
    }

    private RotatingKeyCookieRememberMeManager createRememberMeManager(Serializer serializer){
        RotatingKeyCookieRememberMeManager rememberMeMgr = new RotatingKeyCookieRememberMeManager(CURRENT, NEXT, PREVIOUS, meterRegistry);
        rememberMeMgr.setSerializer(serializer);
        rememberMeMgr.setCipherService(cipherService);

        return rememberMeMgr;
    }

    // Method to initialize the necessary mocks for serialization of the RememberMe cookie
    private ArgumentCaptor<String> initMocksForSerialization(){
        ArgumentCaptor<String> httpHeader = ArgumentCaptor.forClass(String.class);

        when(webDelegatingSubject.getServletRequest()).thenReturn(request);
        when(webDelegatingSubject.getServletResponse()).thenReturn(response);
        when(request.getContextPath()).thenReturn(CONTEXT_PATH);

        return httpHeader;
    }

    // Method to initialize the necessary mocks for serialization of the RememberMe cookie
    private void initMocksForDeserialization(){

        // Prepare Mockito stubbing
        when(webDelegatingSubject.getServletRequest()).thenReturn(request);
        when(webDelegatingSubject.getServletResponse()).thenReturn(response);
        when(request.getContextPath()).thenReturn(CONTEXT_PATH);

        when(webSubjectContext.getServletRequest()).thenReturn(request);
        when(webSubjectContext.getServletResponse()).thenReturn(response);
        when(webSubjectContext.resolveServletRequest()).thenReturn(request);
        when(request.getAttribute(eq(ShiroHttpServletRequest.IDENTITY_REMOVED_KEY))).thenReturn(null);
        when(request.getCookies()).thenReturn(new Cookie[]{ rememberMeCookie });
    }

    private void assertPrincipals(PrincipalCollection principals, String principal, String realm, String value){

        // The GumtreeAccessToken is marked as deprecated but it can be used in serializing / deserializing tests
        // Check the username, token value and realm has been retained
        assertThat(principals.getPrimaryPrincipal(), is(principal));
        assertThat(principals.getRealmNames(), hasItem(realm));
        assertThat(principals.oneByType(GumtreeAccessToken.class).getValue(), is(value));
    }

    // Asserts that the Prometheus Metric for the RememberMe cookie has been called
    private void assertRememberMeMetrics(AuthenticationMetrics.Status status, int expectedValue) {
        Counter counter = meterRegistry.counter(NAME, ACTION, AuthenticationMetrics.Action.REMEMBER_ME_COOKIE.toString(), STATUS, status.toString());

        assertThat(counter.count(), closeTo(expectedValue, 0.1D));
    }

    private UserAuthenticationInfo createAuthenticationInfoFixture(String username, String tokenValue){
        UserAuthenticationInfo authInfo = new UserAuthenticationInfo();
        GumtreeAccessToken gumtreeAccessToken = new GumtreeAccessToken();
        gumtreeAccessToken.setValue(tokenValue);

        authInfo.setUsername(username);
        authInfo.setAccessToken(gumtreeAccessToken);

        return authInfo;
    }

    // Returns the RememberMe cookie value in the supplied HTTP header field
    private String getRememberMeCookieFromHttpHeader(String httpHeader){
        String[] args = httpHeader.split(";");

        for (String arg : args){
            int index = arg.indexOf('=');
            if (SHIRO_REMEMBER_ME_COOKIE_NAME.equals(arg.substring(0, index))){
                return arg.substring(index+1);
            }
        }
        return null;
    }

    private byte[] decrypt(byte[] value, byte[] key) {
        return cipherService.decrypt(Base64.getDecoder().decode(value), key).getBytes();
    }


    private byte[] encrypt(byte[] value, byte[] key) {
        return Base64.getEncoder().encode(cipherService.encrypt(value, key).getBytes());
    }
  
    private String parseJson(byte[] jsonSerialized, String field) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        JsonParser parser = mapper.getFactory().createParser(jsonSerialized);
        JsonNode node = mapper.readTree(parser);
        return node.get(field).textValue();
    }

    private byte[] serializePrincipalsIntoBinary(PrincipalCollection principals) throws IOException  {
        ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
        ObjectOutputStream writeStream = new ObjectOutputStream(byteStream);
        writeStream.writeObject(principals);

        return byteStream.toByteArray();
    }

    private PrincipalCollection getPrincipalCollectionFixture(String principal, String realm, String token){
        List<Object> principals = new ArrayList<>();
        principals.add(principal);
        principals.add(GumtreeAccessToken.createFromString(token));

        return new SimplePrincipalCollection( principals, realm);
    }
}