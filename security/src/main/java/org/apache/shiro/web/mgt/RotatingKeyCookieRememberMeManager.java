package org.apache.shiro.web.mgt;

import io.micrometer.core.instrument.MeterRegistry;
import org.apache.shiro.codec.Base64;
import org.apache.shiro.crypto.CipherService;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SubjectContext;
import org.apache.shiro.util.ByteSource;
import org.apache.shiro.web.servlet.Cookie;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.apache.shiro.web.util.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.gumtree.metrics.AuthenticationMetrics.Action;
import static com.gumtree.metrics.AuthenticationMetrics.Status;
import static com.gumtree.metrics.AuthenticationMetrics.incrementCounter;


public class RotatingKeyCookieRememberMeManager extends CookieRememberMeManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(RotatingKeyCookieRememberMeManager.class);

    private final byte[] previousCipherKey;
    private final byte[] nextEncryptionKey;

    private MeterRegistry meterRegistry;

    public RotatingKeyCookieRememberMeManager(byte[] encryptionKey,
                                              byte[] nextEncryptionKey,
                                              byte[] previousEncryptionKey,
                                              MeterRegistry meterRegistry) {
        this.setCipherKey(encryptionKey);
        this.previousCipherKey = previousEncryptionKey;
        this.nextEncryptionKey = nextEncryptionKey;
        this.meterRegistry = meterRegistry;
    }


    @Override
    public PrincipalCollection getRememberedPrincipals(SubjectContext subjectContext) {
        PrincipalCollection principals = super.getRememberedPrincipals(subjectContext);
        incrementCounter(meterRegistry, Action.REMEMBER_ME_COOKIE, Status.SUCCESS);

        return principals;
    }

    @Override
    protected PrincipalCollection onRememberedPrincipalFailure(RuntimeException e, SubjectContext subjectContext) {

        final byte[] bytes = this.getRememberedSerializedIdentity(subjectContext);
        if(bytes != null && bytes.length > 0 && WebUtils.isHttp(subjectContext)) {
            try {
                // try decrypting using the previous key
                final PrincipalCollection principals = this.convertBytesToPrincipalsUsingSecondaryCipher(bytes);
                this.forgetIdentity(subjectContext);
                rememberIdentity(subjectContext, principals);
                LOGGER.debug("Successfully obtained principals");
                return principals;
            } catch (Exception ex) {
                LOGGER.warn("Failed to decrypt rememberme cookie", ex);
                incrementCounter(meterRegistry, Action.REMEMBER_ME_COOKIE, Status.FAILURE);
            }
        }
        return super.onRememberedPrincipalFailure(e, subjectContext);
    }

    // This is necessary as the super class method rememberIdentity() accepts Subject instead of SubjectContext.
    // For the purpose of setting the cookie, Subject and SubjectContext are interchangeable as long as they
    // have references to the http request and response
    private void rememberIdentity(SubjectContext subjectContext, PrincipalCollection principals) {
        // encrypt using the new key
        final byte[] bytes = convertPrincipalsToBytes(principals);
        // set the cookie
        this.rememberSerializedIdentity(subjectContext, bytes);
    }

    private PrincipalCollection convertBytesToPrincipalsUsingSecondaryCipher(byte[] bytes) {
        byte[] serialized = bytes;
        final CipherService cipherService = this.getCipherService();
        if(cipherService != null) {
            try {
                final ByteSource byteSource = cipherService.decrypt(bytes, previousCipherKey);
                LOGGER.debug("Successfully decrypted cookie using previous key");
                return this.deserialize(byteSource.getBytes());
            } catch (Exception e) {
                final ByteSource byteSource = cipherService.decrypt(bytes, nextEncryptionKey);
                serialized = byteSource.getBytes();
                LOGGER.debug("Successfully decrypted cookie using next key");
            }
        }
        return this.deserialize(serialized);
    }

    private void rememberSerializedIdentity(SubjectContext subject, byte[] serialized) {
        final HttpServletRequest request = WebUtils.getHttpRequest(subject);
        final HttpServletResponse response = WebUtils.getHttpResponse(subject);
        final String base64 = Base64.encodeToString(serialized);
        final Cookie template = this.getCookie();
        final Cookie cookie = new SimpleCookie(template);
        cookie.setValue(base64);
        cookie.saveTo(request, response);
    }
}
