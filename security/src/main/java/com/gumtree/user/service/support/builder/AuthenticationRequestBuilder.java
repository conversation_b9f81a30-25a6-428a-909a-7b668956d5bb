package com.gumtree.user.service.support.builder;

import com.gumtree.user.service.model.AuthenticationProvider;
import com.gumtree.user.service.model.AuthenticationRequest;

import static com.gumtree.web.security.shiro.GumtreeRealm.MWEB_CLIENT;

public final class AuthenticationRequestBuilder {

    private AuthenticationProvider authProvider;
    private String password;
    private String username;
    private String client = MWEB_CLIENT;
    private String threatMetrixSessionId;
    private String ipAddress;

    private AuthenticationRequestBuilder() {

    }

    public static AuthenticationRequestBuilder builder() {
        return new AuthenticationRequestBuilder();
    }

    public AuthenticationRequestBuilder setAuthProvider(AuthenticationProvider authProvider) {
        this.authProvider = authProvider;
        return this;
    }

    public AuthenticationRequestBuilder setPassword(String password) {
        this.password = password;
        return this;
    }

    public AuthenticationRequestBuilder setUsername(String username) {
        this.username = username;
        return this;
    }

    public AuthenticationRequestBuilder setClient(String client) {
        this.client = client;
        return this;
    }

    public AuthenticationRequestBuilder setThreatMetrixSessionId(String threatMetrixSessionId) {
        this.threatMetrixSessionId = threatMetrixSessionId;
        return this;
    }

    public AuthenticationRequestBuilder setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
        return this;
    }

    public AuthenticationRequest build() {
        AuthenticationRequest authenticationRequest = new AuthenticationRequest();
        authenticationRequest.setAuthProvider(authProvider);
        authenticationRequest.setPassword(password);
        authenticationRequest.setUsername(username);
        authenticationRequest.setClient(client);
        authenticationRequest.setThreatMetrixSessionId(threatMetrixSessionId);
        authenticationRequest.setIpAddress(ipAddress);
        return authenticationRequest;
    }

}
