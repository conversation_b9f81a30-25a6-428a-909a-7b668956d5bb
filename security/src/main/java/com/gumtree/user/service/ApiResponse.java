package com.gumtree.user.service;


import com.gumtree.user.service.model.UserApiErrors;

import java.util.Objects;

public abstract class ApiResponse<T> {
    public abstract T get();
    public abstract boolean isDefined();
    public abstract UserApiErrors getError();

    ApiResponse() {
    }

    public static <X> ApiResponse<X> of(X response) {
        if (response == null) {
            throw new IllegalArgumentException("response can't be NULL");
        }

        return new Valid<X>(response);
    }

    public static <X> ApiResponse<X> error(UserApiErrors errors) {
        if (errors == null) {
            throw new IllegalArgumentException("errors can't be NULL");
        }

        return new Error<X>(errors);
    }

    private static class Valid<T> extends ApiResponse<T> {
        private final T value;

        public Valid(T value) {
            this.value = value;
        }

        @Override
        public T get() {
            return value;
        }

        @Override
        public boolean isDefined() {
            return true;
        }

        @Override
        public UserApiErrors getError() {
            throw new UnsupportedOperationException();
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Valid<?> valid = (Valid<?>) o;
            return Objects.equals(value, valid.value);
        }

        @Override
        public int hashCode() {
            return Objects.hash(value);
        }

        @Override
        public String toString() {
            return "Valid{" +
                    "value=" + value +
                    '}';
        }
    }

    public static class Error<T> extends ApiResponse<T> {
        private final UserApiErrors errors;

        Error(UserApiErrors errors) {
            this.errors = errors;
        }

        @Override
        public T get() {
            throw new UnsupportedOperationException();
        }

        @Override
        public boolean isDefined() {
            return false;
        }

        @Override
        public UserApiErrors getError() {
            return errors;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Error<?> error = (Error<?>) o;
            return Objects.equals(errors, error.errors);
        }

        @Override
        public int hashCode() {
            return Objects.hash(errors);
        }

        @Override
        public String toString() {
            return "Error{" +
                    "errors=" + errors +
                    '}';
        }
    }
}
