package com.gumtree.user.service;

import com.gumtree.user.service.api.ThirdPartyMarketingConsentApi;
import com.gumtree.user.service.model.ThirdPartyConsentPreference;
import com.gumtree.util.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Optional;

public class ThirdPartyConsentService {

    private final ThirdPartyMarketingConsentApi thirdPartyMarketingConsentApi;
    private static final Logger LOGGER = LoggerFactory.getLogger(ThirdPartyConsentService.class);

    public ThirdPartyConsentService(ThirdPartyMarketingConsentApi thirdPartyMarketingConsentApi) {
        this.thirdPartyMarketingConsentApi = thirdPartyMarketingConsentApi;
    }

    public ThirdPartyConsentPreference getUserThirdPartyResponse(Long userId) {
        return thirdPartyMarketingConsentApi.getConsent(userId)
                .map(consentPreference -> Optional.ofNullable(consentPreference)
                        .orElse(new ThirdPartyConsentPreference().status(ThirdPartyConsentPreference.StatusEnum.NOT_YET_CONSENTED)))
                .onErrorReturn(this::logErrorAndReturnEmpty)
                .toBlocking()
                .value();
    }

    public ThirdPartyConsentPreference putUserThirdPartyResponse(ThirdPartyConsentPreference userPreference, Long userId){
        return thirdPartyMarketingConsentApi.putConsent(userId, userPreference)
                .onErrorReturn(this::logErrorAndReturnEmpty)
                .toBlocking()
                .value();
    }

    private ThirdPartyConsentPreference logErrorAndReturnEmpty(Throwable error) {
        LOGGER.error("Unexpected error.", ExceptionUtils.toShortMessage(error));
        return new ThirdPartyConsentPreference();
    }
}
