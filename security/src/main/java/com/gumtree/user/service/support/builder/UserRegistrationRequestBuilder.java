package com.gumtree.user.service.support.builder;

import com.gumtree.user.service.model.AuthenticationProvider;
import com.gumtree.user.service.model.UserRegistrationRequest;

import java.time.LocalDate;

public class UserRegistrationRequestBuilder {

    private String accessToken;
    private AuthenticationProvider authenticationProvider;
    private String firstName;
    private String lastName;
    private Boolean optInMarketing;
    private String phoneNumber;
    private String username;
    private Boolean optInThirdPartyMarketing;
    private String threatMetrixSessionId;
    private String ipAddress;
    private LocalDate dateOfBirth;
    private String postcode;

    public static UserRegistrationRequestBuilder builder() {
        return  new UserRegistrationRequestBuilder();
    }

    public UserRegistrationRequestBuilder setAccessToken(String accessToken) {
        this.accessToken = accessToken;
        return this;
    }

    public UserRegistrationRequestBuilder setAuthenticationProvider(AuthenticationProvider authenticationProvider) {
        this.authenticationProvider = authenticationProvider;
        return this;
    }

    public UserRegistrationRequestBuilder setFirstName(String firstName) {
        this.firstName = firstName;
        return this;
    }

    public UserRegistrationRequestBuilder setLastName(String lastName) {
        this.lastName = lastName;
        return this;
    }

    public UserRegistrationRequestBuilder setOptInMarketing(Boolean optInMarketing) {
        this.optInMarketing = optInMarketing;
        return this;
    }

    public UserRegistrationRequestBuilder setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
        return this;
    }

    public UserRegistrationRequestBuilder setUsername(String username) {
        this.username = username;
        return this;
    }

    public UserRegistrationRequestBuilder setOptInThirdPartyMarketing(Boolean optInThirdPartyMarketing) {
        this.optInThirdPartyMarketing = optInThirdPartyMarketing;
        return this;
    }

    public UserRegistrationRequestBuilder setThreatMetrixSessionId(String threatMetrixSessionId) {
        this.threatMetrixSessionId = threatMetrixSessionId;
        return this;
    }

    public UserRegistrationRequestBuilder setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
        return this;
    }

    public UserRegistrationRequestBuilder setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
        return this;
    }

    public UserRegistrationRequestBuilder setPostcode(String postcode) {
        this.postcode = postcode;
        return this;
    }


    public UserRegistrationRequest build() {
        UserRegistrationRequest userRegistrationRequest = new UserRegistrationRequest();
        userRegistrationRequest.setUsername(username);
        userRegistrationRequest.setPhoneNumber(phoneNumber);
        userRegistrationRequest.setOptInMarketing(optInMarketing);
        userRegistrationRequest.setLastName(lastName);
        userRegistrationRequest.setFirstName(firstName);
        userRegistrationRequest.setAccessToken(accessToken);
        userRegistrationRequest.setAuthenticationProvider(authenticationProvider);
        userRegistrationRequest.setOptInThirdPartyMarketing(optInThirdPartyMarketing);
        userRegistrationRequest.setThreatMetrixSessionId(threatMetrixSessionId);
        userRegistrationRequest.setIpAddress(ipAddress);
        userRegistrationRequest.setDateOfBirth(dateOfBirth);
        userRegistrationRequest.setPostcode(postcode);
        return userRegistrationRequest;
    }


}
