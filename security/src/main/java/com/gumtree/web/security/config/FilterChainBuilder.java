package com.gumtree.web.security.config;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Private class for building filter chains.
 */
public class FilterChainBuilder {

    private List<String> filterChainDefinitions = new ArrayList<String>();

    public FilterChainBuilder containing(FilterDefinitionBuilder definitionBuilder) {
        filterChainDefinitions.add(definitionBuilder.build());
        return this;
    }

    public FilterChainBuilder containing(FilterDefinitionBuilder... definitionBuilders) {
        return definitionBuilders != null ? containing(Arrays.asList(definitionBuilders)) : this;
    }

    public FilterChainBuilder containing(List<FilterDefinitionBuilder> definitionBuilders) {
        if (definitionBuilders != null && definitionBuilders.size() > 0) {
            definitionBuilders.forEach(definitionBuilder -> containing(definitionBuilder));
        }
        return this;
    }

    public String build() {
        StringBuilder builder = new StringBuilder();
        for (String filterChainDefinition : filterChainDefinitions) {
            builder.append(filterChainDefinition);
            builder.append("\n");
        }
        return builder.toString();
    }
}
