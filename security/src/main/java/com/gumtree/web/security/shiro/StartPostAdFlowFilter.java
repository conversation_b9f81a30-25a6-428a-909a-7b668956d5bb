package com.gumtree.web.security.shiro;

import com.gumtree.web.security.SecurityHelper;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.security.shiro.filter.BaseCustomLoginFilter;
import org.apache.shiro.subject.Subject;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

/**
 * Filter for ensuring new user has gone through login page to start post ad flow, or the user
 * is a logged in existing user.
 */
public final class StartPostAdFlowFilter extends BaseCustomLoginFilter {

    private LoginUtils loginUtils;
    private SecurityHelper securityHelper;

    /**
     * Constructor.
     *
     * @param loginUtils login utils helper
     */
    public StartPostAdFlowFilter(LoginUtils loginUtils, SecurityHelper securityHelper) {
        this.loginUtils = loginUtils;
        this.securityHelper = securityHelper;
    }

    @Override
    protected boolean internalIsAccessAllowed(ServletRequest request, ServletResponse response) {
        Subject subject = getSubject(request, response);
        return letNewUserPassThrough()
                || ((subject.isRemembered() || subject.isAuthenticated()) && securityHelper.verifyAccessTokenAndLogoutIfInvalid(subject));
    }

    private boolean letNewUserPassThrough() {
        return loginUtils.getNewUserEmailAddressFromSession() != null
                && !loginUtils.newUserMustLoginToStartPostAdFlow();
    }
}
