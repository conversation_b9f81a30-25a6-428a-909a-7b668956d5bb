package com.gumtree.web.security.shiro;

import com.google.common.collect.Lists;
import com.gumtree.userapi.model.GumtreeAccessToken;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.springframework.util.Assert;

import java.util.List;

public class AppAuthenticationInfo implements AuthenticationInfo {
    private String username;
    private PrincipalCollection principalCollection;
    private GumtreeAccessToken accessToken;

    @Override
    public PrincipalCollection getPrincipals() {
        if (principalCollection == null) {
            Assert.hasLength(username);

            List<Object> principals = Lists.<Object>newArrayList(username);
            if (accessToken != null) {
                principals.add(accessToken);
            }
            principalCollection = new SimplePrincipalCollection(principals, GumtreeAppTokenRealm.GUMTREE_REALM_NAME);
        }
        return principalCollection;
    }

    @Override
    public Object getCredentials() {
        return accessToken.getValue();
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getUsername() {
        return username;
    }

    public GumtreeAccessToken getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(GumtreeAccessToken accessToken) {
        this.accessToken = accessToken;
    }
}
