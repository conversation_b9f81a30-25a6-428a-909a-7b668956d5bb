package com.gumtree.web.security.shiro;

import com.gumtree.common.properties.GtProps;
import com.gumtree.common.properties.utils.PropSupplier;
import com.gumtree.common.util.security.exception.MissingPasswordException;
import com.gumtree.common.util.security.exception.MissingUsernameAndPasswordException;
import com.gumtree.common.util.security.exception.MissingUsernameException;
import com.gumtree.common.util.security.exception.UserAccountSuspendedException;
import com.gumtree.config.SellerProperty;
import com.gumtree.recaptcha.RecaptchaValidationResult;
import com.gumtree.recaptcha.RecaptchaValidator;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.AuthenticationProvider;
import com.gumtree.user.service.model.AuthenticationRequest;
import com.gumtree.user.service.model.AuthenticationResponse;
import com.gumtree.user.service.model.RegisteredUser;
import com.gumtree.user.service.model.UserApiErrorCode;
import com.gumtree.user.service.model.UserApiErrors;
import com.gumtree.user.service.model.UserRegistrationRequest;
import com.gumtree.user.service.support.builder.AuthenticationRequestBuilder;
import com.gumtree.user.service.support.builder.UserRegistrationRequestBuilder;
import com.gumtree.userapi.model.GumtreeAccessToken;
import com.gumtree.web.security.exception.UserApiAuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.realm.AuthenticatingRealm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import static com.gumtree.user.service.model.AuthenticationProvider.FACEBOOK;
import static com.gumtree.user.service.model.AuthenticationProvider.GOOGLE;
import static com.gumtree.user.service.model.AuthenticationProvider.GOOGLEID;


/**
 * Security realm that authenticates via the Bushfire API.
 */
public final class GumtreeRealm extends AuthenticatingRealm {
    private static final Logger LOGGER = LoggerFactory.getLogger(GumtreeRealm.class);
    public static final String GUMTREE_REALM_NAME = "gumtree";
    public static final String MWEB_CLIENT = "mweb";

    private final PropSupplier<Boolean> recaptchaEnabled = GtProps.getDBool(SellerProperty.RECAPTCHA_ENABLED);
    private final UserServiceFacade userServiceFacade;
    private final RecaptchaValidator recaptchaValidator;

    public GumtreeRealm(UserServiceFacade userServiceFacade, RecaptchaValidator recaptchaValidator) {
        this.userServiceFacade = userServiceFacade;
        this.recaptchaValidator = recaptchaValidator;
    }

    @Override
    public Class getAuthenticationTokenClass() {
        return GumtreeAuthenticationToken.class;
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return token != null && (GumtreeAuthenticationToken.class.isAssignableFrom(token.getClass())
                || UsernamePasswordToken.class.isAssignableFrom(token.getClass()));
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) {

        if (token instanceof GumtreeAuthenticationToken) {
            GumtreeAuthenticationToken gtToken = (GumtreeAuthenticationToken) token;

            recaptchaChecks(gtToken);

            validate(gtToken.getUsername(), gtToken.getPassword());

            AuthenticationRequest authenticationRequest = AuthenticationRequestBuilder.builder()
                    .setUsername(gtToken.getUsername())
                    .setPassword(gtToken.getPassword())
                    .setAuthProvider(gtToken.getAuthenticationProvider())
                    .setClient(MWEB_CLIENT)
                    .setThreatMetrixSessionId(gtToken.getThreatmetrixSessionId().orElse(null))
                    .setIpAddress(gtToken.getIpAddress().orElse(null))
                    .build();

            return authenticate(authenticationRequest, true);

        } else if (token instanceof UsernamePasswordToken) {
            UsernamePasswordToken gtToken = (UsernamePasswordToken) token; // triggered on password change
            String passwd = gtToken.getPassword() != null ? String.valueOf(gtToken.getPassword()) : null;
            validate(gtToken.getUsername(), passwd);

            AuthenticationRequest req = AuthenticationRequestBuilder.builder()
                    .setUsername(gtToken.getUsername())
                    .setPassword(passwd)
                    .setAuthProvider(AuthenticationProvider.GUMTREE)
                    .setClient(MWEB_CLIENT)
                    .build();
            return authenticate(req, true);
        }

        throw new IllegalArgumentException("unsupported token type: " + token.getClass().getName());
    }

    private void recaptchaChecks(GumtreeAuthenticationToken gtToken) {
        if (recaptchaEnabled.get() && AuthenticationProvider.GUMTREE.equals(gtToken.getAuthenticationProvider())) {
            String clientRecaptcha = gtToken.getRecaptchaResponse();
            if (StringUtils.hasText(clientRecaptcha) && gtToken.getIpAddress().isPresent()) {
                RecaptchaValidationResult result = recaptchaValidator
                        .validateResponse(clientRecaptcha, gtToken.getIpAddress().get());
                if (result == RecaptchaValidationResult.INVALID) {
                    throw new RecaptchaAuthenticationException("Recaptcha Validation Failed");
                }
            } else {
                throw new RecaptchaAuthenticationException("Recaptcha Parameters Missing");
            }
        }
    }

    private AuthenticationInfo registerAndThenAuthenticate(AuthenticationRequest authRequest) {
        UserRegistrationRequest regRequest = UserRegistrationRequestBuilder.builder()
                .setUsername(authRequest.getUsername())
                .setAccessToken(authRequest.getPassword())
                .setThreatMetrixSessionId(authRequest.getThreatMetrixSessionId())
                .setAuthenticationProvider(authRequest.getAuthProvider()).build();
        LOGGER.info("registerAndThenAuthenticate,tmxId:{}",regRequest.getThreatMetrixSessionId());
        ApiResponse<RegisteredUser> regResp = userServiceFacade.registerUser(regRequest);
        if (regResp.isDefined()) {
            return authenticate(authRequest, false);
        } else {
            throw new UserApiAuthenticationException("Auto registration error: " + regResp.getError().getErrorCode());
        }
    }

    private AuthenticationInfo authenticate(AuthenticationRequest req, boolean autoRegisterOnError) {

        ApiResponse<AuthenticationResponse> resp = userServiceFacade.authenticate(req);
        if (resp.isDefined()) {
            UserAuthenticationInfo uai = new UserAuthenticationInfo();
            uai.setUsername(resp.get().getUsername());
            com.gumtree.user.service.model.GumtreeAccessToken gumtreeAccessToken = resp.get().getGumtreeAccessToken();
            if (gumtreeAccessToken != null && gumtreeAccessToken.getValue() != null) {
                uai.setAccessToken(GumtreeAccessToken.createFromString(gumtreeAccessToken.getValue()));
            }
            return uai;
        } else {
            if (autoRegisterOnError) {
                UserApiErrors apiErrors = resp.getError();
                if (isSocial(req.getAuthProvider())
                        && (UserApiErrorCode.USER_NOT_FOUND.name().equals(apiErrors.getErrorCode())
                        || UserApiErrorCode.USER_INACTIVE.name().equals(apiErrors.getErrorCode()))) {

                    return registerAndThenAuthenticate(req);
                }
            }

            if (UserApiErrorCode.USER_SUSPENDED.name().equals(resp.getError().getErrorCode())) {
                throw new UserAccountSuspendedException();
            }

            throw new IncorrectCredentialsException(resp.getError().getErrorCode());
        }
    }

    private void validate(String username, String password) {
        boolean usernameIsDefined = StringUtils.hasLength(username);
        boolean passwordIsDefined = StringUtils.hasLength(password);

        if (!usernameIsDefined && !passwordIsDefined) {
            throw new MissingUsernameAndPasswordException();
        } else if (!usernameIsDefined) {
            throw new MissingUsernameException();
        } else if (!passwordIsDefined) {
            throw new MissingPasswordException();
        }
    }

    @Override
    public String getName() {
        return GUMTREE_REALM_NAME;
    }

    private boolean isSocial(AuthenticationProvider value) {
        return GOOGLE.equals(value) || GOOGLEID.equals(value) || FACEBOOK.equals(value);
    }
}

