package com.gumtree.web.security.testing;

import com.google.common.base.Optional;
import com.gumtree.api.User;

import javax.servlet.http.HttpServletRequest;

public class SocialLoginUtil {


    // rename from: isNewlyActivatedSocialLoginRequiringMarketingOptIn
    public static boolean isRequiringMarketingOptIn(HttpServletRequest request,
                                                    Optional<User> user,
                                                    boolean isSeller) {
        if (user.isPresent() && user.get().getActivationDate() != null) {
            return isSeller ?
                    newActiUserSocialAndNotYetOptMarketing(user.get())
                            && (isComingFromLoginPage(request) || isComingFromCreateAccountPage(request))
                    : newActiUserSocialAndNotYetOptMarketing(user.get());
        }
        return false;
    }

    private static boolean newActiUserSocialAndNotYetOptMarketing(User user) {
        return !user.isOptInMarketing() &&
                isCreationAndActivationDatesEqual(user) &&
                isActivationDateWithinLastMinute(user) &&
                !user.getSocialData().isEmpty();
    }

    private static boolean isComingFromLoginPage(HttpServletRequest request) {
        return isComingFromPage(request, "login");
    }

    private static boolean isComingFromCreateAccountPage(HttpServletRequest request) {
        return isComingFromPage(request, "create-account");
    }

    private static boolean isComingFromPage(HttpServletRequest request, String page) {
        return request.getHeader("referer") != null && request.getHeader("referer").endsWith(page);
    }

    private static boolean isActivationDateWithinLastMinute(User user) {
        return user.getActivationDate().isBeforeNow() && user.getActivationDate().plusMinutes(1).isAfterNow();
    }

    private static boolean isCreationAndActivationDatesEqual(User user) {
        return user.getCreationDate().getMillis() == user.getActivationDate().getMillis();
    }

}
