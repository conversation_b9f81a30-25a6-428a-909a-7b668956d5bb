worker_processes  1;

events {
    worker_connections  1024;
}

http {
    server {
        listen 8443;
        server_name ~^((?<service>.*)\.)((?<environment>.*)\.)(localhost-proxy.gumtree).io$;

        location / {
            resolver ******* ipv6=off;

            proxy_http_version 1.1;
            proxy_set_header Host "proxy-$service.thirdparty.$environment.gumtree.io";
            proxy_ssl_name "proxy-$service.thirdparty.$environment.gumtree.io";
            proxy_ssl_server_name on;
            proxy_ssl_session_reuse off;

            add_header X-Host "proxy-$service.thirdparty.$environment.gumtree.io";

            proxy_pass "https://proxy-$service.thirdparty.$environment.gumtree.io";
        }
    }

}