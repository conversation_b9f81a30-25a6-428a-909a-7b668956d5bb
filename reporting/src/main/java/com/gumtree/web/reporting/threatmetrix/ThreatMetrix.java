package com.gumtree.web.reporting.threatmetrix;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation used to trigger ThreatMetrix tracking reporting on a page request.
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface ThreatMetrix {

    /**
     * Name of the model key that can be used to disable TM per request bases. The model value for the key must be
     * valid boolean svalue
     *
     * @return the model key
     */
    String enabledKey() default "";
}
