package com.gumtree.web.zeno;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.Ad;
import com.gumtree.common.util.StringUtils;
import com.gumtree.common.util.search.AdvertClickSource;
import com.gumtree.domain.advert.Advert;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.AttributeValue;
import com.gumtree.domain.user.UserType;
import com.gumtree.fulladsearch.model.FullAdFeature;
import com.gumtree.fulladsearch.model.FullAdFlatAd;
import com.gumtree.mobile.web.storage.SessionDataService;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.zeno.core.domain.AdvertData;
import com.gumtree.zeno.core.domain.UserData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.gumtree.zeno.core.domain.AdvertData.anAdvert;
import static com.gumtree.zeno.core.domain.UserData.aUser;

/**
 */
@Component
public class ToAdvertDataConverter {

    @Autowired
    private SessionDataService sessionDataService;

    public AdvertData flatAdToAdvertData(FullAdFlatAd flatAd) {
        Map<String, String> attributes = Maps.newHashMap();

        if (flatAd.getAttribute() != null) {
            for (Map.Entry<String, Object> attribute : flatAd.getAttribute().entrySet()) {
                attributes.put(attribute.getKey(), attribute.getValue().toString());
            }
        }

        int imageCount = StringUtils.hasText(flatAd.getPrimaryImageUrl()) ? 1 : 0;
        imageCount += (flatAd.getAdditionalImageUrls() != null ? flatAd.getAdditionalImageUrls().size() : 0);

        AdvertClickSource clickSource = sessionDataService.getAdvertClickThroughType(flatAd.getId());

        return anAdvert()
                .withId(flatAd.getId())
                .withCreatedDate(flatAd.getCreatedDate())
                .withLastPublishedDate(flatAd.getPublishedDate())
                .withDescriptionLength(flatAd.getDescription().length())
                .withImageCount(imageCount)
                .withYoutubeLink(flatAd.getYoutubeUrl())
                .withReplyUrl(flatAd.getContactUrl())
                .withSeller(aUser()
                        .withUserEmail(flatAd.getContactEmail())
                        .withAccountId(flatAd.getAccount().getId())
                        .withAccountType(flatAd.getAccount().getPro()
                                ? UserData.AccountType.Pro
                                : UserData.AccountType.Standard)
                        .build())
                .withAttributes(attributes)
                .withActiveFeatures(getFeaturesForAdvert(flatAd))
                .withAdvertClickSource(clickSource.getParameterValue())
                .build();
    }

    public AdvertData advertToAdvertData(Advert advert) {
        Map<String, String> attributes = Maps.newHashMap();

        for (Attribute attribute : advert.getAttributes()) {
            AttributeValue value = attribute.getValue();
            attributes.put(attribute.getType(), value.as(String.class));
        }

        Long publishedDate = (advert.getLiveDate() != null ? advert.getLiveDate().getTime() : null);
        AdvertClickSource clickSource = sessionDataService.getAdvertClickThroughType(advert.getId());

        return anAdvert()
                .withId(advert.getId())
                .withLastPublishedDate(publishedDate)
                .withDescriptionLength(advert.getDescription().length())
                .withImageCount(advert.getImages().size())
                .withYoutubeLink(advert.getVideos().isEmpty() ? null : advert.getVideos().iterator().next().getUrl())
                .withReplyUrl(advert.getPostingUser().getContactUrl())
                .withSeller(aUser()
                                .withUserEmail(advert.getPostingUser().getEmailAddress())
                                .withAccountType(advert.getPostingUser().getType() == UserType.TRADE
                                        ? UserData.AccountType.Pro
                                        : UserData.AccountType.Standard)
                                .build()
                )
                .withAttributes(attributes)
                .withActiveFeatures(getFeaturesForAdvert(advert))
                .withAdvertClickSource(clickSource.getParameterValue())
                .build();
    }

    private List<String> getFeaturesForAdvert(FullAdFlatAd flatAd) {
        List<String> products = null;

        if (flatAd.getFeatures() != null) {
            // We are only concerned about whether the advert is urgent or has website
            Iterable<FullAdFeature> concernedFeatures = flatAd.getFeatures().stream()
                    .filter(input -> {
                        ProductName product = ProductName.valueOf(input.getProduct());
                        return (ProductName.URGENT == product || ProductName.WEBSITE_URL == product);
                    }).collect(Collectors.toList());
            products = Lists.newArrayList(StreamSupport.stream(concernedFeatures.spliterator(), false)
                    .map(FullAdFeature::getProduct)
                    .collect(Collectors.toList()));
        }

        return products == null || products.isEmpty() ? null : products;
    }

    private List<String> getFeaturesForAdvert(Advert advert) {
        List<String> products = Lists.newArrayList();

        if (StringUtils.hasText(advert.getWebsiteLink())) {
            products.add(ProductName.WEBSITE_URL.name());
        }

        if (advert.isUrgent()) {
            products.add(ProductName.URGENT.name());
        }

        return products.isEmpty() ? null : products;
    }


    public AdvertData adToAdvertData(Ad ad) {
        Map<String, String> attributes = Maps.newHashMap();

        if (ad.getAttributes() != null) {
            for (com.gumtree.api.Attribute attribute : ad.getAttributes()) {
                attributes.put(attribute.getName(), attribute.getValue());
            }
        }

        return anAdvert()
                .withId(ad.getId())
                .withCreatedDate(ad.getCreationDate().getMillis())
                .withDescriptionLength(ad.getDescription().length())
                .withImageCount(ad.getImages().size())
                .withYoutubeLink(ad.getYoutubeLink())
                .withReplyUrl(ad.getContactUrl())
                .withAttributes(attributes)
                .build();
    }
}
