package com.gumtree.web.zeno.userregistration;

import com.google.common.base.Objects;
import com.gumtree.zeno.core.event.ZenoEvent;

public class UserActivationFailureZenoEvent implements ZenoEvent {

    private String emailAddress;

    public UserActivationFailureZenoEvent() {
    }

    public UserActivationFailureZenoEvent(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserActivationFailureZenoEvent that = (UserActivationFailureZenoEvent) o;
        return Objects.equal(emailAddress, that.emailAddress);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(emailAddress);
    }
}