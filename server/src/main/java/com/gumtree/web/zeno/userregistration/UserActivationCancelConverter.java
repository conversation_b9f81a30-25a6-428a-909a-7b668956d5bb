package com.gumtree.web.zeno.userregistration;

import com.gumtree.zeno.core.converter.AbstractEventConverter;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserActivationCancel;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserActivationCancelConverter extends AbstractEventConverter<String[], String, UserActivationCancel> {

    private RequestDetailsService requestDetailsService;

    @Autowired
    public UserActivationCancelConverter(ZenoConverterService zenoConverterService,
                                         RequestDetailsService requestDetailsService) {
        super(UserActivationCancel.class, zenoConverterService);
        this.requestDetailsService = requestDetailsService;
    }

    @Override
    public UserActivationCancel convertToEvent(String[] input, String output) {
        PageData pageData = requestDetailsService.getPageData(PageType.Unknown);
        UserData userData = requestDetailsService.getUserData();
        DeviceData deviceData = requestDetailsService.getDeviceData();

        return new UserActivationCancel(pageData, userData, deviceData);
    }

}
