package com.gumtree.web.seller.page.postad.model;

import com.gumtree.api.category.domain.Category;

import java.util.Comparator;

import static com.gumtree.domain.category.Categories.COMMUNITY;
import static com.gumtree.domain.category.Categories.FLATS_AND_HOUSES;
import static com.gumtree.domain.category.Categories.FOR_SALE;
import static com.gumtree.domain.category.Categories.JOBS;
import static com.gumtree.domain.category.Categories.MOTORS;
import static com.gumtree.domain.category.Categories.PETS;
import static com.gumtree.domain.category.Categories.SERVICES;

public class AlphabeticalCategoryComparator implements Comparator<Category> {

    private static final String OTHER = "OTHER";
    private static final String CARS_VANS_MOTORBIKES_SEO = MOTORS.getSeoName();
    private static final String FLATS_HOUSES_SEO = FLATS_AND_HOUSES.getSeoName();
    private static final String JOBS_SEO = JOBS.getSeoName();
    private static final String SERVICES_SEO = SERVICES.getSeoName();
    private static final String PETS_SEO = PETS.getSeoName();
    private static final String COMMUNITY_SEO = COMMUNITY.getSeoName();
    private static final String FOR_SALE_SEO = FOR_SALE.getSeoName();

    @Override
    public final int compare(Category postAdCategory1, Category postAdCategory2) {

        //L1 have a specific Order
        if (CARS_VANS_MOTORBIKES_SEO.equals(postAdCategory1.getSeoName())) {
            return -8;
        } else if (CARS_VANS_MOTORBIKES_SEO.equals(postAdCategory2.getSeoName())) {
            return 8;
        }

        if (FLATS_HOUSES_SEO.equals(postAdCategory1.getSeoName())) {
            return -7;
        } else if (FLATS_HOUSES_SEO.equals(postAdCategory2.getSeoName())) {
            return 7;
        }

        if (FOR_SALE_SEO.equals(postAdCategory1.getSeoName())) {
            return -6;
        } else if (FOR_SALE_SEO.equals(postAdCategory2.getSeoName())) {
            return 6;
        }

        if (JOBS_SEO.equals(postAdCategory1.getSeoName())) {
            return -5;
        } else if (JOBS_SEO.equals(postAdCategory2.getSeoName())) {
            return 5;
        }

        if (SERVICES_SEO.equals(postAdCategory1.getSeoName())) {
            return -4;
        } else if (SERVICES_SEO.equals(postAdCategory2.getSeoName())) {
            return 4;
        }

        if (PETS_SEO.equals(postAdCategory1.getSeoName())) {
            return -3;
        } else if (PETS_SEO.equals(postAdCategory2.getSeoName())) {
            return 3;
        }

        if (COMMUNITY_SEO.equals(postAdCategory1.getSeoName())) {
            return -2;
        } else if (COMMUNITY_SEO.equals(postAdCategory2.getSeoName())) {
            return 2;
        }

        //Other is Always to be put at the Bottom
        if (postAdCategory1.getSeoName().toUpperCase().contains(OTHER)) {
            return 1;
        } else if (postAdCategory2.getSeoName().toUpperCase().contains(OTHER)) {
            return -1;
        }

        return postAdCategory1.getDisplayName().toUpperCase().compareTo(postAdCategory2.getDisplayName().toUpperCase());
    }
}
