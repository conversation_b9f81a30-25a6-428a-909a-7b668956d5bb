package com.gumtree.web.seller.page.postad.controller;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.gumtree.api.Account;
import com.gumtree.api.SellerType;
import com.gumtree.api.ValueResult;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdAttribute;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeValue;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.service.pricing.PricingContextImpl;
import com.gumtree.web.seller.service.pricing.PricingService;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Map;

import static com.gumtree.common.util.messages.FSBOMessages.FPAD_FREE_LINK;
import static com.gumtree.common.util.messages.FSBOMessages.FPAD_SUSPECTED_WHY_DESCRIPTION;
import static com.gumtree.common.util.messages.FSBOMessages.FPAD_SUSPECTED_WHY_TITLE;
import static com.gumtree.common.util.messages.FSBOMessages.FPAD_WHY_EXPLAIN;
import static com.gumtree.common.util.messages.FSBOMessages.FPAD_WHY_SELECTED;

public class MandatorySellerTypePanel implements SellerTypePanel {
    private static final String SELLER_TYPE_TRADE = "TRADE";
    private static final String SELLER_TYPE_PRIVATE = "PRIVATE";
    private static final String FPAD_FREE_CONTENT_DYNAMIC =
            "Private sellers can always have one ad active in this category for free.\n" +
                    "As you already have one ad active in this category, posting another will cost £%s.\n\n" +
                    "If you don't need the other ad anymore, you can delete it in " +
                    "'<a href=\"/manage/ads\" target=\"blank\">Manage My Ads</a>' and post this ad for free.";

    private final SellerType sellerType;
    private final AdvertEditor editor;
    private final String label;
    private final String tradeValue;
    private final String privateValue;
    private final AttributeMetadata attributeMetadata;
    private final PricingService pricingService;
    private final CategoryModel categoryModel;
    private final Account account;

    public MandatorySellerTypePanel(BushfireApi bushfireApi,
                                    AdvertEditor editor,
                                    AttributeMetadata attributeMetadata,
                                    PricingService pricingService,
                                    CategoryModel categoryModel) {
        this.attributeMetadata = attributeMetadata;
        this.pricingService = pricingService;
        this.categoryModel = categoryModel;
        this.account = getAccount(bushfireApi, editor);;
        this.sellerType = checkSellerType(bushfireApi, editor);
        this.editor = editor;
        this.label = attributeMetadata.getSyi() != null && attributeMetadata.getSyi().getLabel() != null
                ? attributeMetadata.getSyi().getLabel()
                : attributeMetadata.getLabel();
        this.tradeValue = attributeMetadata.getSrp().getFilters().get(0).getValues().get(0).getValue();
        this.privateValue = attributeMetadata.getSrp().getFilters().get(0).getValues().get(1).getValue();
    }

    private SellerType checkSellerType(BushfireApi bushfireApi, AdvertEditor editor) {
        if (this.account == null) {
            return SellerType.NO_INFO;
        }
        ValueResult<SellerType> sType = bushfireApi.accountApi().getSellerType(this.account.getId(), editor.getCategoryId());
        return sType.getValue();
    }

    private Account getAccount(BushfireApi bushfireApi, AdvertEditor editor) {
        return editor.getAccountId() != null ? bushfireApi.accountApi().getAccount(editor.getAccountId()) : null;
    }

    @Override
    public String getId() {
        return "seller-type-group";
    }

    @Override
    public String getLabel() {
        return label;
    }


    @Override
    public Map<String, String> getPopUp() {
        if (sellerType == SellerType.BUSINESS) {
            return ImmutableMap.of("title", FPAD_WHY_SELECTED, "body", FPAD_WHY_EXPLAIN, "link", FPAD_WHY_SELECTED);
        } else if (sellerType == SellerType.PRIVATE_WITH_LIVE_AD) {
            PricingMetadata priceInformation = pricingService.getPriceInformation(
                    new PricingContextImpl(editor, categoryModel, SellerType.PRIVATE_WITH_LIVE_AD));
            return ImmutableMap.of("title", FPAD_FREE_LINK, "body",
                    String.format(FPAD_FREE_CONTENT_DYNAMIC, priceInformation.getInsertionPrice().getPrice()));
        } else {
            return null;
        }
    }

    @Override
    public Map<String, String> getText() {
        if (sellerType == SellerType.SUSPECTED_BUSINESS) {
            return ImmutableMap.of("title", FPAD_SUSPECTED_WHY_TITLE, "body", FPAD_SUSPECTED_WHY_DESCRIPTION);
        } else {
            return null;
        }
    }

    @Override
    public PostAdAttribute getAttribute() {
        PostAdAttribute postAdAttribute = new PostAdAttribute();
        postAdAttribute.setId(getId());
        postAdAttribute.setMandatory(true);
        postAdAttribute.setPriceSensitive(true);

        String selectedValue = editor.getPostAdFormBean().getAttributes().get(CategoryConstants.Attribute.SELLER_TYPE.getName());
        Integer advertLimit = editor.getPostAdFormBean().getAdvertLimit();
        Integer advertCount = editor.getPostAdFormBean().getAdvertCount();

        if (advertLimit != null) {
            // Check if an advert limit was placed on this category
            String sellerTypeValue;
            if (isSellerTypeSelected()){
                // Check if user selected radio button value
                sellerTypeValue = selectedValue.toUpperCase();
            } else {
                // Use seller type returned from call from BAPI
                sellerTypeValue =  editor.getPostAdFormBean().getDefaultSellerType();
            }
            postAdAttribute.setValues(getAttrValues(sellerTypeValue, advertLimit, advertCount==null?0:advertCount));
        }
        else if (sellerType == SellerType.BUSINESS) {
            postAdAttribute.setValues(getAttrValues(tradeValue, true));
        } else if (sellerType == SellerType.SUSPECTED_BUSINESS) {
            postAdAttribute.setValues(getAttrValues(ObjectUtils.firstNonNull(selectedValue, tradeValue), false));
            // get either current selection or trade
        } else {
            postAdAttribute.setLabel(label);
            postAdAttribute.setValues(getAttrValues(selectedValue, false));
        }


        return postAdAttribute;
    }

    @Override
    public boolean isSellerTypeSelected() {
        if (editor.isEditMode()) {
            return true;
        }
        return SellerType.BUSINESS == sellerType
                || SellerType.SUSPECTED_BUSINESS == sellerType
                || editor.getPostAdFormBean().getAttributes().get(CategoryConstants.Attribute.SELLER_TYPE.getName()) != null;
    }

    @Override
    public String getSellerType() {
        if (SellerType.BUSINESS == sellerType || SellerType.SUSPECTED_BUSINESS == sellerType) {
            return tradeValue;
        } else {
            if (editor.getPostAdFormBean().getAttributes().get(CategoryConstants.Attribute.SELLER_TYPE.getName()) != null) {
                return editor.getPostAdFormBean().getAttributes().get(CategoryConstants.Attribute.SELLER_TYPE.getName());
            } else {
                return null;
            }
        }
    }

    private ImmutableList<PostAdAttributeValue> getAttrValues(String selectedValue, int advertLimit, int advertCount) {
        String tradeLabel = attributeMetadata.getSyi().getValues().get(0).getLabel();
        String privateLabel = attributeMetadata.getSyi().getValues().get(1).getLabel();
        boolean isPrivateDisabled;

        if (advertCount >= advertLimit && !account.isPro()) {
            // If above or on the advert limit force to trade and disable private
            selectedValue = SELLER_TYPE_TRADE;
            isPrivateDisabled = true;
        } else {
            // Default to private if not set
            selectedValue = selectedValue==null?SELLER_TYPE_PRIVATE:selectedValue;
            isPrivateDisabled = false;
        }

        return ImmutableList.of(
                new PostAdAttributeValue(tradeValue, tradeLabel, SELLER_TYPE_TRADE.equals(selectedValue), false),
                new PostAdAttributeValue(privateValue, privateLabel, SELLER_TYPE_PRIVATE.equals(selectedValue), isPrivateDisabled));
    }

    private ImmutableList<PostAdAttributeValue> getAttrValues(String selectedValue, boolean disablePrivateIfNotSelected) {
        String tradeLabel = attributeMetadata.getSyi().getValues().get(0).getLabel();
        String privateLabel = attributeMetadata.getSyi().getValues().get(1).getLabel();

        boolean isTradeValueSelected = tradeValue.equals(selectedValue);
        boolean isPrivateValueSelected = privateValue.equals(selectedValue);
        if (editingPropertyToRentOrToShare()) {
            return ImmutableList.of(
                    new PostAdAttributeValue(tradeValue, tradeLabel, isTradeValueSelected, !isTradeValueSelected),
                    new PostAdAttributeValue(privateValue, privateLabel, isPrivateValueSelected, !isPrivateValueSelected));
        } else {
            return ImmutableList.of(
                    new PostAdAttributeValue(tradeValue, tradeLabel, isTradeValueSelected, false),
                    new PostAdAttributeValue(privateValue, privateLabel, isPrivateValueSelected,
                            !isPrivateValueSelected && disablePrivateIfNotSelected));
        }

    }

    private boolean editingPropertyToRentOrToShare() {
        long propertyToShareCategoryId = 12181L;
        long propertyToRentCategoryId = 12183L;
        return editor.isEditMode() && (editor.getCategoryId().equals(propertyToShareCategoryId) ||
                editor.getCategoryId().equals(propertyToRentCategoryId));
    }
}
