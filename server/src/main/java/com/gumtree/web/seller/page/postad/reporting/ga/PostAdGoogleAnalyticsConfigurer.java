package com.gumtree.web.seller.page.postad.reporting.ga;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsConfigurer;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder;
import com.gumtree.web.reporting.google.ga.events.impl.PostAdBegin;
import com.gumtree.web.seller.page.postad.reporting.ga.events.PostAdFreeAttempt;
import com.gumtree.web.seller.page.postad.reporting.ga.events.PostAdPreview;
import org.springframework.stereotype.Component;

@Component
public class PostAdGoogleAnalyticsConfigurer implements GoogleAnalyticsConfigurer<Void> {

    @Override
    public void configure(GoogleAnalyticsReportBuilder reportBuilder, ThirdPartyRequestContext<Void> ctx) {
        reportBuilder
                .pageType(ctx.getPageType())
                .l1Category(ctx.getL1Category())
                .category(ctx.getCategory())
                .county(ctx.getCounty())
                .addTrackEvent(new PostAdPreview(ctx))
                .addTrackEvent(new PostAdFreeAttempt(ctx))
                .addTrackEvent(new PostAdBegin(ctx));
    }

}
