package com.gumtree.web.seller.page.ajax.category;

import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.service.ai.AISuggestService;
import com.gumtree.util.HeadersExtensions;
import com.gumtree.web.abtest.growthbook.ClientExperiments;
import com.gumtree.web.seller.service.category.CategorySuggesterService;
import com.gumtree.web.seller.service.category.model.CategorySuggesterScoreModel;
import com.gumtree.web.seller.service.category.model.CategoryTextMatcher;
import com.gumtree.web.seller.service.jobs.JobsSuggesterService;
import com.gumtree.web.seller.storage.DraftAdvertService;
import lombok.extern.slf4j.Slf4j;

import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * CategorySuggesterController.
 */
@Slf4j
@RestController
public class CategorySuggesterController {
    @Autowired
    private CustomMetricRegistry metrics;

    private static final String SERVICES_TITLE = "Services";
    private static final int TOTAL_SUGGESTIONS_TO_DISPLAY = 5;

    @Autowired
    private CategoryModel categoryModel;
    @Autowired
    private JobsSuggesterService jobsSuggesterService;
    @Autowired
    private CategorySuggesterService categorySuggesterService;
    @Autowired
    private AISuggestService aiSuggestService;
    @Autowired
    private DraftAdvertService draftAdvertService;

    @RequestMapping(value = "/api/category/suggest", method = RequestMethod.GET)
    public SuggestedCategories suggestCategory(@RequestParam(value = "input")
                                               final String input,
                                               HttpServletRequest request) {
        ClientExperiments clientExperiments = HeadersExtensions.getClientExperiments(request);
        log.info("suggestCategory clientExperiments:{} /n",
                clientExperiments.toGamExperimentsString());

        List<SuggestedCategory> scatsfinal = new ArrayList<>();


        if (clientExperiments.isB(ClientExperiments.ExperimentEnum.SYI_CATE_SUG_FLAG)) {
            int experimentBlimit = TOTAL_SUGGESTIONS_TO_DISPLAY;
            List<SuggestedCategory> categoryListAll = categorySuggesterService
                    .getSuggestedCategoriesAllName(input, 1);

            log.info("suggestCategory B categoryListAll size：{}", categoryListAll.size());
            List<SuggestedCategory> suggestTextMatchCategories = categorySuggesterService
                    .getSuggestedTextMatchCategories(input, true, experimentBlimit);

            log.info("suggestCategory B suggestTextMatchCategories size：{}", suggestTextMatchCategories.size());
            List<SuggestedCategory> categoryPredictList = categorySuggesterService
                    .getCategoryPredictApi(input, experimentBlimit);
            log.info("suggestCategory B categoryPredictList size：{}", categoryPredictList.size());

            scatsfinal.addAll(categoryListAll);
            scatsfinal.addAll(categoryPredictList);
            scatsfinal.addAll(suggestTextMatchCategories);
            scatsfinal = rankCategoryCandidate(input, scatsfinal, experimentBlimit);
        } else if (clientExperiments.isC(ClientExperiments.ExperimentEnum.SYI_CATE_SUG_FLAG)) {

            int experimentClimit = TOTAL_SUGGESTIONS_TO_DISPLAY;
            List<SuggestedCategory> categoryListAll = categorySuggesterService
                    .getSuggestedCategoriesAllName(input, 1);
            log.info("suggestCategory C categoryListAll size：{}", categoryListAll.size());
            List<SuggestedCategory> categoryPredictList = categorySuggesterService
                    .getCategoryPredictApi(input, experimentClimit);
            log.info("suggestCategory C suggestTextMatchCategories size：{}", categoryPredictList.size());

            List<SuggestedCategory> suggestTextMatchCategories = categorySuggesterService
                    .getSuggestedTextMatchCategories(input, true, experimentClimit);
            log.info("suggestCategory C categoryPredictList size：{}", suggestTextMatchCategories.size());

            // TODO: remove this rule after updating category name of sofa in services (category id 11254)
            String normalizedQuery = java.util.Optional.ofNullable(input).map(String::trim).map(String::toLowerCase).orElse("");
            if (!"sofa".equals(normalizedQuery)) {
                scatsfinal.addAll(categoryListAll);
            }

            scatsfinal.addAll(categoryPredictList);
            scatsfinal.addAll(suggestTextMatchCategories);
            scatsfinal = deduplicateByIdKeepFirst(scatsfinal).stream().limit(experimentClimit).collect(Collectors.toList());
        } else if (clientExperiments.isD(ClientExperiments.ExperimentEnum.SYI_CATE_SUG_FLAG)) {

            int experimentBlimit = TOTAL_SUGGESTIONS_TO_DISPLAY;
            List<SuggestedCategory> categoryListAll = categorySuggesterService.getSuggestedCategoriesAllName(input, 1);

            log.info("suggestCategory D categoryListAll size：{}", categoryListAll.size());
            List<SuggestedCategory> suggestTextMatchCategories = categorySuggesterService
                    .getSuggestedTextMatchCategories(input, false, experimentBlimit);

            log.info("suggestCategory D suggestTextMatchCategories size：{}", suggestTextMatchCategories.size());
            List<SuggestedCategory> categoryPredictList = categorySuggesterService.getCategoryPredictApi(input, experimentBlimit);
            log.info("suggestCategory D categoryPredictList size：{}", categoryPredictList.size());

            scatsfinal.addAll(categoryListAll);
            scatsfinal.addAll(suggestTextMatchCategories);
            scatsfinal.addAll(categoryPredictList);
            scatsfinal = deduplicateByIdKeepFirst(scatsfinal).stream().limit(experimentBlimit).collect(Collectors.toList());
        } else {
            List<Long> suggestedCatIds = categorySuggesterService.getSuggestedCategories(input);
            List<SuggestedCategory> sCats = new ArrayList<>();
            for (Long catId : suggestedCatIds) {
                Optional<Category> cat = categoryModel.getCategory(catId);

                if (cat.isPresent() && isAdPostingPermitted(cat.get())) {
                    String breadcrumb = categoryModel.getBreadcrumb(cat.get().getId(), ";");
                    SuggestedCategory sCat = new SuggestedCategory(cat.get().getId(), cat.get().getDisplayName(), breadcrumb);
                    sCats.add(sCat);
                }
            }
            List<SuggestedCategory> suggestedJobCategories = jobsSuggesterService.getSuggestedJobCategories(input, suggestedCatIds);
            scatsfinal = composeFinalJobAndCategorySuggestions(suggestedJobCategories, sCats);
        }
        logCategoryCrumb(scatsfinal, clientExperiments.toGamExperimentsString());
        return new SuggestedCategories(scatsfinal);
    }

    /**
     * Just for AI Post, two entrance ,one is title first, the other is photo first,the input is title or photos
     *
     * @param input title or photos
     * @param type  img or title
     * @return SuggestedCategories
     */
    @RequestMapping(value = "/api/category/AIsuggest", method = RequestMethod.GET)
    @ResponseBody
    public SuggestedCategories AISuggestCategory(@RequestParam(value = "input") final String input, @RequestParam(value = "type") final String type) {
        long startTime = System.currentTimeMillis();
        List<Long> suggestedCatIds = aiSuggestService.getSuggestedCategories(input, type);
        log.info("AISuggestCategory->input:{},type:{},response:{}", input, type, suggestedCatIds);
        List<SuggestedCategory> sCats = new ArrayList<>();
        if (!CollectionUtils.isEmpty(suggestedCatIds)) {
            for (Long catId : suggestedCatIds) {
                Optional<Category> cat = categoryModel.getCategory(catId);
                log.info("categoryById:catId={},CategoryPresent={}", catId, cat.isPresent());
                if (!cat.isPresent()) {
                    continue;
                }
                log.info("categoryById:catId={},category={}", catId, cat.get());
                if (isAdPostingPermitted(cat.get())) {
                    String breadcrumb = categoryModel.getBreadcrumb(catId, ",");
                    SuggestedCategory sCat = new SuggestedCategory(catId, cat.get().getDisplayName(), breadcrumb);
                    sCats.add(sCat);
                }
            }
            logCategoryCrumb(sCats, "AIPost");

            long endTime = System.currentTimeMillis();
            metrics.AIResponseTimer("PredictCate").record(endTime - startTime, TimeUnit.MILLISECONDS);
        }
        return new SuggestedCategories(sCats);
    }

    @RequestMapping(value = "/api/categoryAttributes/AIsuggest", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, String> AISuggestCategoryAttributes(@RequestParam(value = "input") final String input, @RequestParam(value = "type") final String type,
                                                           @RequestParam(value = "categoryName") final String categoryName) {
        Map<String, String> suggestedAttributes = new HashMap<>();
        suggestedAttributes = metrics.AIResponseTimer("PredictCategoryAttributes").record(() -> aiSuggestService.getSuggestedAttributes(input, type, categoryName));

        //update draft
        Optional<PostAdDetail> postAdDetailOptional = draftAdvertService.retrieve();
        if (postAdDetailOptional.isPresent()) {
            PostAdDetail postAdDetail = postAdDetailOptional.get();
            PostAdFormBean postAdFormBean = Optional.fromNullable(postAdDetail.getPostAdFormBean())
                    .or(new PostAdFormBean());
            postAdFormBean.setAttributes(suggestedAttributes);
            postAdDetail.setPostAdFormBean(postAdFormBean);

            boolean isPersisted = draftAdvertService.persist(postAdDetail);
            if (!isPersisted) {
                log.warn("AISuggestCategoryAttributes-> Failed to persist updated PostAdDetail with new attributes");
            }
        } else {
            log.info("AISuggestCategoryAttributes-> No existing draft found");
        }

        return suggestedAttributes;
    }

    private List<SuggestedCategory> composeFinalJobAndCategorySuggestions(List<SuggestedCategory> suggestedJobCategories,
                                                                          List<SuggestedCategory> sCats) {
        int numberOfJobCategoriesToDisplay = Math.min(1, suggestedJobCategories.size());
        int numberOfNormalCategoriesToDisplay = Math.min(TOTAL_SUGGESTIONS_TO_DISPLAY - numberOfJobCategoriesToDisplay, sCats.size());

        List<SuggestedCategory> suggestedJobCategoriesFinal = suggestedJobCategories.subList(0, numberOfJobCategoriesToDisplay);
        List<SuggestedCategory> sCatsFinal = sCats.subList(0, numberOfNormalCategoriesToDisplay);

        OptionalInt indexOpt = jobsSuggesterService.getIndexPositionToInsert(sCatsFinal, SERVICES_TITLE);
        int indexJobToInsert = Math.min(indexOpt.orElse(numberOfNormalCategoriesToDisplay), numberOfNormalCategoriesToDisplay);

        return applyIndexPositionBasedMerge(sCats, suggestedJobCategoriesFinal, indexJobToInsert, numberOfNormalCategoriesToDisplay);
    }

    private List<SuggestedCategory> applyIndexPositionBasedMerge(List<SuggestedCategory> sCats,
                                                                 List<SuggestedCategory> suggestedJobCategoriesFinal,
                                                                 int indexJobToInsert,
                                                                 int numberOfNormalCategoriesToDisplay) {
        List<SuggestedCategory> sCatsFinalAboveServices = new ArrayList<>(sCats.subList(0, indexJobToInsert));
        List<SuggestedCategory> sCatsFinalBelowServices = new ArrayList<>(sCats.subList(indexJobToInsert,
                numberOfNormalCategoriesToDisplay));

        sCatsFinalAboveServices.addAll(suggestedJobCategoriesFinal);
        sCatsFinalAboveServices.addAll(sCatsFinalBelowServices);
        return sCatsFinalAboveServices;
    }

    public static boolean isAdPostingPermitted(Category category) {
        return (category.getEnabled() == null || category.getEnabled())
                && (category.getReadOnly() == null || !category.getReadOnly())
                && !category.isHidden()
                && category.isAdPostingPermitted();
    }

    /***
     * add Extended Fields
     */
    public void logCategoryCrumb(List<SuggestedCategory> suggestedCategoryList, String experimentStr) {
        for (SuggestedCategory suggestedCategory : suggestedCategoryList) {
            Map<String, String> logparams = new LinkedHashMap<>();
            if (suggestedCategory.getCategoryCrumb() != null) {
                logparams.putAll(suggestedCategory.getCategoryCrumb());
            }
            logparams.put("experiment", experimentStr);

            Optional<Category> categoryOptional = categoryModel.getCategory(suggestedCategory.getId());

            if (categoryOptional.isPresent()) {
                logparams.put("ad_category_id", categoryOptional.get().getId().toString());
                logparams.put("ad_category", categoryOptional.get().getName());
            }

            List<Category> hierarchyList = categoryModel.getFullPath(suggestedCategory.getId());
            if (!hierarchyList.isEmpty()) hierarchyList.remove(0);
            int level = 1;
            for (Category listCategory : hierarchyList) {
                logparams.put("ad_subcategory" + level, listCategory.getId() + "");
                level++;
            }
            suggestedCategory.setCategoryCrumb(logparams);
        }
    }

    //duplicate removal
    public static List<SuggestedCategory> deduplicateByIdKeepFirst(List<SuggestedCategory> list) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        return new ArrayList<>(list.stream()
                .collect(Collectors.toMap(
                        SuggestedCategory::getId,
                        category -> category,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ))
                .values());
    }

    static List<SuggestedCategory> rankCategoryCandidate(String query, List<SuggestedCategory> list, int topK) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        String normalizedQuery = java.util.Optional.ofNullable(query).map(String::trim).map(String::toLowerCase).orElse("");
        List<SuggestedCategory> deduplicateCategoryList = deduplicateByIdKeepFirst(list);
        List<CategorySuggesterScoreModel> categories = deduplicateCategoryList.stream().map(
                        category -> new CategorySuggesterScoreModel(
                                category.getId(), category.getDisplayName(), category.getTree(), category.getCategoryCrumb()))
                .collect(Collectors.toList());

        CategoryTextMatcher matcher = new CategoryTextMatcher(normalizedQuery, categories, topK, 0.5, 2, 0.76);
        List<SuggestedCategory> result = new ArrayList<>();
        for (CategorySuggesterScoreModel category : matcher.doTextMatch()) {
            result.add(new SuggestedCategory(category.getId(), category.getDisplayName(), category.getTree(), category.getCategoryCrumb()));
        }
        return result;
    }
}