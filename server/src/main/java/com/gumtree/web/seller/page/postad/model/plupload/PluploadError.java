package com.gumtree.web.seller.page.postad.model.plupload;

/**
 */
public final class PluploadError {

    private int code;

    private String message = "";

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        PluploadError that = (PluploadError) o;

        if (code != that.code) return false;
        return message != null ? message.equals(that.message) : that.message == null;

    }

    @Override
    public int hashCode() {
        int result = code;
        result = 31 * result + (message != null ? message.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "PluploadError{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }

    /**
     * Builder class for the error
     */
    public static final class Builder {

        private int code;

        private String message;

        /**
         * Set a code for the error
         *
         * @param code - the code
         * @return this
         */
        public Builder withCode(int code) {
            this.code = code;
            return this;
        }

        /**
         * Set a message for the error
         *
         * @param message - the message
         * @return this
         */
        public Builder message(String message) {
            this.message = message;
            return this;
        }

        /**
         * Build the error object
         *
         * @return new error
         */
        public PluploadError build() {
            PluploadError error = new PluploadError();
            error.code = code;
            error.message = message;
            return error;
        }
    }

    /**
     * Make a new builder for a new error
     *
     * @return builder
     */
    public static Builder aPluploadError() {
        return new Builder();
    }
}
