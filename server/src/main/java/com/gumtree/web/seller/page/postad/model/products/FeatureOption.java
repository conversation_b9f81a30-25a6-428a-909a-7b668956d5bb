package com.gumtree.web.seller.page.postad.model.products;

import java.util.List;

/**
 * Represents a feature option within post/edit ad flow.
 */
public interface FeatureOption {

    /**
     * @return if this feature is already active within the advert being edited. Always false when posting new ads.
     */
    boolean isActive();

    /**
     * @return returns expiry description if the feature is already active.
     */
    String getExpiryDescription();

    /**
     * @return a general description of this feature option.
     */
    String getDescription();

    /**
     * @return the type of this feature option.
     */
    ProductType getType();

    /**
     * @return the list of possible prices for this feature option.
     */
    List<ProductPrice> getPrices();
}
