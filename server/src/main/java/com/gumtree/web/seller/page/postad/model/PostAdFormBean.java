package com.gumtree.web.seller.page.postad.model;

import com.gumtree.api.User;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.web.seller.page.common.model.Form;
import com.gumtree.web.seller.page.postad.model.features.FeatureBean;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import com.gumtree.web.seller.page.postad.validation.AtLeastOneTrue;
import com.gumtree.web.seller.page.postad.validation.NotEmptyWhenFlagged;
import com.gumtree.web.seller.page.postad.validation.ValidPrice;
import com.gumtree.web.seller.page.postad.validation.WebsiteURLFeatureValidation;
import org.apache.commons.lang.StringUtils;
import org.codehaus.jackson.annotate.JsonAnySetter;
import org.codehaus.jackson.annotate.JsonIgnore;
import org.codehaus.jackson.annotate.JsonProperty;

import java.io.Serializable;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@NotEmptyWhenFlagged.List({
        @NotEmptyWhenFlagged(field = "contactEmail", flagField = "useEmail", message = "postad.email.missing"),
        @NotEmptyWhenFlagged(field = "contactTelephone", flagField = "usePhone", message = "postad.telephone.missing"),
        @NotEmptyWhenFlagged(field = "contactUrl", flagField = "useUrl", message = "postad.contacturl.missing")
})
@WebsiteURLFeatureValidation(field = "websiteUrl", message = "postad.wensiteurl.needs_website")
@AtLeastOneTrue(fieldList = { "useEmail", "usePhone", "useUrl" }, message = "postad.contact.neither.selected")
public class PostAdFormBean extends Form implements Serializable {
    private Map<String, Object> fields = new HashMap<>();

    private Long categoryId;
    private Long locationId;
    private String postcode;
    private Boolean visibleOnMap;
    private String area;

    private Boolean termsAgreed;
    private String title;
    private String description;

    private String previousContactName;
    private String contactName;
    private String previousContactEmail;
    private String contactEmail;
    private String contactTelephone;
    private String contactUrl;
    private boolean useEmail;
    private boolean usePhone;
    private boolean useUrl;

    private String checkoutVariationId;

    private Long mainImageId;

    private List<Long> imageIds = new LinkedList<>();

    private String youtubeLink;
    private String websiteUrl = "http://";

    // register user fields
    private String firstName;
    private String lastName;
    private String emailAddress;
    private String telephoneNumber;
    private String password;
    private Boolean optInMarketing;
    private Boolean inAutobizFlow;
    private String defaultSellerType;
    private Integer advertLimit;
    private Integer advertCount;

    public VrmStatus getVrmStatus() {
        return vrmStatus;
    }

    public void setVrmStatus(VrmStatus vrmStatus) {
        this.vrmStatus = vrmStatus;
    }

    private VrmStatus vrmStatus = VrmStatus.VRM_NONE;
    @JsonIgnore
    private Boolean submitForm;

    @ValidPrice(message = "postad.price.invalid")
    private Map<String, String> attributes = new HashMap<>();

    private Map<ProductType, FeatureBean> features = new LinkedHashMap<>();

    private Map<String,String> extendFields=new ConcurrentHashMap<>();

    /**
     * Default constructor.
     */
    public PostAdFormBean() {

    }

    /**
     * Constructor.
     *
     * @param user initialise with contact info from User
     */
    public PostAdFormBean(User user, String preferredEmail) {
        setVisibleOnMap(true);
        setEmailAddress(user.getEmail());
        setContactTelephone(user.getPhone());
        setContactName(user.getFirstName());
        setVrmStatus(VrmStatus.VRM_NONE);
        if (user.getStatus() != null && user.getStatus().equals(UserStatus.ACTIVE)) {
            setOptInMarketing(user.isOptInMarketing());
        } else {
            setOptInMarketing(true);
        }


        if (StringUtils.isNotBlank(preferredEmail)) {
            setContactEmail(preferredEmail);
            setUseEmail(true);
        }

    }

    public String getDefaultSellerType() {
        return defaultSellerType;
    }

    public void setDefaultSellerType(String defaultSellerType) {
        this.defaultSellerType = defaultSellerType;
    }

    public Integer getAdvertLimit() {
        return advertLimit;
    }

    public void setAdvertLimit(Integer advertLimit) {
        this.advertLimit = advertLimit;
    }

    public Integer getAdvertCount() {
        return advertCount;
    }

    public void setAdvertCount(Integer advertCount) {
        this.advertCount = advertCount;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }


    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        if(postcode != null) {
            this.postcode = postcode.trim();
        } else {
            this.postcode = postcode;
        }
    }

    public Boolean isTermsAgreed() {
        return termsAgreed;
    }

    public void setTermsAgreed(Boolean termsAgreed) {
        this.termsAgreed = termsAgreed;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPreviousContactEmail() {
        return previousContactEmail;
    }

    public void setPreviousContactEmail(String previousContactEmail) {
        this.previousContactEmail = previousContactEmail;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getContactTelephone() {
        return contactTelephone;
    }

    public void setContactTelephone(String contactTelephone) {
        this.contactTelephone = contactTelephone;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public boolean isUseEmail() {
        return useEmail;
    }

    public void setUseEmail(boolean useEmail) {
        this.useEmail = useEmail;
    }

    public boolean isUsePhone() {
        return usePhone;
    }

    public void setUsePhone(boolean usePhone) {
        this.usePhone = usePhone;
    }

    public String getCheckoutVariationId() {
        return checkoutVariationId;
    }

    public void setCheckoutVariationId(String checkoutVariationId) {
        this.checkoutVariationId = checkoutVariationId;
    }

    public Long getMainImageId() {
        return mainImageId;
    }

    public void setMainImageId(Long mainImageId) {
        this.mainImageId = mainImageId;
    }

    public Boolean getVisibleOnMap() {
        return visibleOnMap;
    }

    public void setVisibleOnMap(Boolean visibleOnMap) {
        this.visibleOnMap = visibleOnMap;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public Boolean getInAutobizFlow() {
        return inAutobizFlow;
    }

    public void setInAutobizFlow(Boolean inAutobizFlow) {
        this.inAutobizFlow = inAutobizFlow;
    }

    /**
     * Getter for attributes. Initialises if not already.
     *
     * @return attributes map.
     */
    public Map<String, String> getAttributes() {
        if (attributes == null) {
            attributes = new HashMap<>();
        }
        return attributes;
    }

    public void setAttributes(Map<String, String> attributes) {
        this.attributes = attributes;
    }

    public Map<ProductType, FeatureBean> getFeatures() {
        return features;
    }

    public void setFeatures(Map<ProductType, FeatureBean> features) {
        this.features = features;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getTelephoneNumber() {
        return telephoneNumber;
    }

    public void setTelephoneNumber(String telephoneNumber) {
        this.telephoneNumber = telephoneNumber;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Boolean isOptInMarketing() {
        return optInMarketing;
    }

    public void setOptInMarketing(Boolean optInMarketing) {
        this.optInMarketing = optInMarketing;
    }

    public String getPreviousContactName() {
        return previousContactName;
    }

    public void setPreviousContactName(String previousContactName) {
        this.previousContactName = previousContactName;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactUrl() {
        return contactUrl;
    }

    public void setContactUrl(String contactUrl) {
        this.contactUrl = contactUrl;
    }

    public boolean isUseUrl() {
        return useUrl;
    }

    public void setUseUrl(boolean useUrl) {
        this.useUrl = useUrl;
    }

    public String getYoutubeLink() {
        return youtubeLink;
    }

    public void setYoutubeLink(String youtubeLink) {
        this.youtubeLink = youtubeLink;
    }

    public String getWebsiteUrl() {
        return websiteUrl;
    }

    public void setWebsiteUrl(String websiteUrl) {
        this.websiteUrl = websiteUrl;
    }

    public String addAttribute(String key, String value){
        return attributes.put(key, value);
    }

    @JsonIgnore
    public Boolean isSubmitForm() {
        return submitForm;
    }

    @JsonProperty("submitForm")
    public void setSubmitForm(Boolean submitForm) {
        this.submitForm = submitForm;
    }

    @JsonAnySetter
    public void handleUnknownProperties(String key, Object value) {
        fields.put(key, value);
    }

    public Object getUnknownProperties(String key) {
        return fields.get(key);
    }

    public List<Long> getImageIds() {
        return imageIds;
    }

    @JsonIgnore
    public List<Long> getExistingImageIds() {
        return imageIds.stream().filter(id -> id != null && id != 0).collect(Collectors.toList());
    }

    public void setImageIds(List<Long> imageIds) {
        this.imageIds = imageIds;
    }

    public Map<String, String> getExtendFields() {
        return extendFields;
    }

    public void setExtendFields(Map<String, String> extendFields) {
        this.extendFields = extendFields;
    }

    public void addExtendField(String key ,String value){
        this.extendFields.put(key, value);
    }

    public void addImageOnProperPosition(int position, Long bapiImageId) {

        if (imageIds.stream().noneMatch(i -> i != null && i.equals(bapiImageId))) {
            if (position < imageIds.size() && (imageIds.get(position) == null || imageIds.get(position) == 0)) {
                imageIds.set(position, bapiImageId);
            } else {
                for (int i = imageIds.size(); i < position; i++) {
                    imageIds.add(null);
                }
                imageIds.add(bapiImageId);
            }
        }
    }

    public boolean removeImage(Long imageIdToRemove) {
        if(imageIdToRemove != null && imageIds != null) {
            return imageIds.removeIf(existingImageId -> existingImageId != null
                    && existingImageId.equals(imageIdToRemove));
        }
        return false;
    }
}
