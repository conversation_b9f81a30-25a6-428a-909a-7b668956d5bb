package com.gumtree.web.seller.page.postad.model.products;

import com.google.common.base.Predicate;
import com.google.common.collect.Iterables;
import com.google.common.collect.Maps;

import com.gumtree.common.util.time.Clock;
import com.gumtree.web.common.format.PostingTimeFormatter;

import org.joda.time.DateTime;

import java.util.List;
import java.util.Map;

import javax.annotation.Nullable;

/**
 * Default implementation of {@link PricingMetadata}.
 * <p/>
 * Note: used by {@link com.gumtree.web.seller.service.pricing.ApiPricingService} and test coverage provided by it.
 */
public final class DefaultPricingMetadata implements PricingMetadata {

    private ProductPrice insertionPrice;

    private ProductPrice bumpUpPrice;

    private Map<ProductType, FeatureOption> features = Maps.newHashMap();

    /**
     * Private constructor - use {@link Builder} to create instances.
     *
     * @param insertionPrice the price for insertion
     * @param bumpUpPrice    the price for bump up
     * @param features       the built features for this metadata object
     */
    public DefaultPricingMetadata(
            ProductPrice insertionPrice,
            ProductPrice bumpUpPrice,
            Map<ProductType, FeatureOption> features) {

        this.insertionPrice = insertionPrice;
        this.bumpUpPrice = bumpUpPrice;
        this.features = features;
    }

    @Override
    public ProductPrice getInsertionPrice() {
        return insertionPrice;
    }

    @Override
    public ProductPrice getBumpUpPrice() {
        return bumpUpPrice;
    }

    @Override
    public Map<ProductType, FeatureOption> getFeatureOptions() {
        return features;
    }

    @Override
    public ProductPrice getFeaturePrice(ProductType type, String productName) {

        FeatureOption featureOption = getFeatureOption(type);
        if (featureOption != null) {
            if (featureOption.getPrices() != null) {
                return Iterables.find(featureOption.getPrices(), new ProductPriceFilter(productName));
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public FeatureOption getFeatureOption(ProductType type) {
        return features.get(type);
    }

    @Override
    public FeatureOption getFeatureOption(String type) {
        return features.get(ProductType.valueOf(type));
    }


    /**
     * For filtering {@link ProductPrice}s by product name.
     */
    private static final class ProductPriceFilter implements Predicate<ProductPrice> {

        private String productName;

        /**
         * Constructor.
         *
         * @param productName the product name
         */
        private ProductPriceFilter(String productName) {
            this.productName = productName;
        }

        @Override
        public boolean apply(@Nullable ProductPrice input) {

            return productName.equals(input.getProductName());
        }
    }

    /**
     * Builder for {@link DefaultPricingMetadata}.
     */
    public static final class Builder {

        private Clock clock;

        /**
         * Constructor.
         *
         * @param clock the clock for date/time operations.
         */
        public Builder(Clock clock) {
            this.clock = clock;
        }

        private ProductPrice insertionPrice;

        private ProductPrice bumpUpPrice;

        private Map<ProductType, FeatureOption> features = Maps.newHashMap();

        /**
         * Set insertion product price.
         *
         * @param price the price
         * @return this
         */
        public Builder withInsertionPrice(ProductPrice price) {
            insertionPrice = price;
            return this;
        }


        /**
         * Set bump up product price.
         *
         * @param price the price
         * @return this
         */
        public Builder withBumpUpPrice(ProductPrice price) {
            bumpUpPrice = price;
            return this;
        }

        /**
         * Build with a new feature.
         *
         * @param type     the feature type
         * @param products the associated products
         * @return this
         */
        public Builder withNonActiveFeature(ProductType type, List<ProductPrice> products) {

            features.put(type, new DefaultFeature(type, products));

            return this;
        }

        /**
         * Build with an existing active feature
         *
         * @param type       the feature type
         * @param expiryDate when the feature is due to expire
         * @return this
         */
        public Builder withActiveFeature(ProductType type, DateTime expiryDate) {

            features.put(type, new DefaultFeature(type, PostingTimeFormatter.formatFeatureExpiryTime(
                    expiryDate, new DateTime(clock.now()))));

            return this;
        }

        /**
         * @return a newly constructed {@link DefaultPricingMetadata}
         */
        public PricingMetadata build() {
            return new DefaultPricingMetadata(insertionPrice, bumpUpPrice, features);
        }
    }

    /**
     * Default implementation of {@link FeatureOption}
     */
    private static final class DefaultFeature implements FeatureOption {

        private ProductType type;

        private List<ProductPrice> products;

        private String expiryDescription;

        private boolean active = false;

        /**
         * Constructor for non-active features.
         *
         * @param type     the type of feature
         * @param products the product prices associated with this feature
         */
        private DefaultFeature(ProductType type, List<ProductPrice> products) {
            this.type = type;
            this.products = products;
        }

        /**
         * Constructor for already active features.
         *
         * @param type              the type of feature
         * @param expiryDescription the description pertaining to when the feature will expire
         */
        private DefaultFeature(ProductType type, String expiryDescription) {
            this.type = type;
            this.expiryDescription = expiryDescription;
            this.active = true;
        }

        @Override
        public boolean isActive() {
            return active;
        }

        @Override
        public String getExpiryDescription() {
            return expiryDescription;
        }

        @Override
        public String getDescription() {
            return type.getDescription();
        }

        @Override
        public ProductType getType() {
            return type;
        }

        @Override
        public List<ProductPrice> getPrices() {
            return products;
        }
    }
}
