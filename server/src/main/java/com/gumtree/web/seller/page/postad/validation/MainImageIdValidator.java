package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.seller.page.postad.model.PostAdFormBean;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class MainImageIdValidator implements ConstraintValidator<MainImageIdValidation, PostAdFormBean> {

    private String message;

    private String field;

    @Override
    public final void initialize(MainImageIdValidation constraintAnnotation) {
        this.message = constraintAnnotation.message();
        this.field = constraintAnnotation.field();
    }

    @Override
    public final boolean isValid(PostAdFormBean value, ConstraintValidatorContext context) {
        return (value.getImageIds().isEmpty() && value.getMainImageId() == null) ||
                value.getImageIds().contains(value.getMainImageId());
    }
}
