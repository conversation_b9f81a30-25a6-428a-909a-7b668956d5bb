package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.common.util.StringUtils;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import org.mvel2.MVEL;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 *
 */
public class NotEmptyWhenFlaggedValidator
        implements ConstraintValidator<NotEmptyWhenFlagged, PostAdFormBean> {
    private String field;
    private String flagField;
    private String message;


    @Override
    public final void initialize(NotEmptyWhenFlagged constraintAnnotation) {
        this.field = constraintAnnotation.field();
        this.flagField = constraintAnnotation.flagField();
        this.message = constraintAnnotation.message();
    }

    @Override
    public final boolean isValid(PostAdFormBean value, ConstraintValidatorContext context) {
        Object fieldObj = MVEL.getProperty(field, value);
        Object flagFieldObj = MVEL.getProperty(flagField, value);

        boolean flagSet = Boolean.parseBoolean(flagFieldObj.toString());

        if (flagSet) {
            if (fieldObj == null || !StringUtils.hasText(fieldObj.toString())) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(message)
                        .addNode(field)
                        .addConstraintViolation();
                return false;
            }
        }

        return true;
    }
}
