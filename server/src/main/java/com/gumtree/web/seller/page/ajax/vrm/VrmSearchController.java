package com.gumtree.web.seller.page.ajax.vrm;

import com.codahale.metrics.MetricRegistry;
import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.web.seller.page.ajax.vrm.model.VrmLookupResponse;
import com.gumtree.web.seller.page.ajax.vrm.model.attribute.VrmAttributes;
import com.gumtree.web.seller.page.common.model.SummaryAttribute;
import com.gumtree.web.seller.page.postad.model.PriceGuidance;
import com.gumtree.web.seller.page.postad.model.PriceGuidanceContext;
import com.gumtree.web.seller.service.postad.priceguidance.PriceGuidanceService;
import javaslang.Tuple;
import javaslang.Tuple2;
import javaslang.collection.HashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.MOTORBIKE_MAKE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_BODY_TYPE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_COLOUR;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_DOORS;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_ENGINE_SIZE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_ESTIMATED_MILEAGE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_FUEL_TYPE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_MAKE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_MODEL;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_REGISTRATION_YEAR;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_TRANSMISSION;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VRN;
import static com.gumtree.web.common.domain.converter.Conversions.createAttribute;
import static com.gumtree.web.seller.page.common.model.SummaryAttribute.builder;

@Controller
public class VrmSearchController {

    private static final Logger LOGGER = LoggerFactory.getLogger(VrmSearchController.class);

    private final CategoryModel categoryModel;
    private final PriceGuidanceService priceGuidanceService;
    private final MetricRegistry metricRegistry;

    @Autowired
    public VrmSearchController(CategoryModel categoryModel,
                               PriceGuidanceService priceGuidanceService,
                               MetricRegistry metricRegistry) {
        this.categoryModel = categoryModel;
        this.priceGuidanceService = priceGuidanceService;
        this.metricRegistry = metricRegistry;
    }

    @RequestMapping(value = "/ajax/vrn", method = RequestMethod.GET)
    @ResponseBody()
    public VrmLookupResponse lookupVrn(
            @RequestParam(value = "input") final String vrn,
            @RequestParam(value = "categoryId") final Long categoryId) throws Exception {
        try {
            return vehicleDataResponseAsVrnLookupResponse(vrn, categoryId);
        } catch (Exception ex) {
            LOGGER.error("Error fetching VehicleData", ex);
            return VrmLookupResponse.fail();
        }
    }

    private VrmLookupResponse vehicleDataResponseAsVrnLookupResponse(String vrn,
                                                                     Long categoryId) {
        java.util.Optional<PriceGuidanceContext> priceGuidanceContext = priceGuidanceService.getForCarAd(vrn, categoryId);

        measurePriceGuidanceSuccess(priceGuidanceContext);

        return priceGuidanceContext.map(PriceGuidanceContext::getVehicleAttributes)
                .map(attr -> VrmLookupResponse.success(asVrmAttributes(categoryId, attr),
                        priceGuidanceContext.map(PriceGuidanceContext::getPriceGuidance)))
                .orElseGet(VrmLookupResponse::fail);
    }

    private VrmAttributes asVrmAttributes(Long categoryId, Map<String, String> attributes) {
        Tuple2<VrmAttributes.Builder, HashMap<String, Consumer<SummaryAttribute>>> builders = getAttributeBuilders();
        List<AttributeMetadata> attributeMetadata = categoryModel.getCategoryAttributes(categoryId).or(Lists.newArrayList());
        attributes.entrySet().stream().map(entry -> createAttribute(entry.getKey(), entry.getValue(), attributeMetadata, builder()))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .forEach(summaryAttribute -> builders._2.get(summaryAttribute.getAttributeName()).forEach(f -> f.accept(summaryAttribute)));
        return builders._1.build();
    }

    private void measurePriceGuidanceSuccess(java.util.Optional<PriceGuidanceContext> priceGuidance) {
        // collect discovery stats, delete once we release to prod and we learn enough
        priceGuidance.ifPresent(ctx -> {
            PriceGuidance item = ctx.getPriceGuidance();
            if (item.getTotalAds() == 0) {
                metricRegistry.counter("gumtree.price_suggestion.available.none").inc();
            }

            if (item.getTotalAds() > 0) {
                metricRegistry.counter("gumtree.price_suggestion.available.over_0").inc();
            }

            if (item.getTotalAds() > 1) {
                metricRegistry.counter("gumtree.price_suggestion.available.over_1").inc();
            }

            if (item.getTotalAds() > 2) {
                metricRegistry.counter("gumtree.price_suggestion.available.over_2").inc();
            }

            if (item.getTotalAds() > 3) {
                metricRegistry.counter("gumtree.price_suggestion.available.over_3").inc();
            }

            metricRegistry.counter("gumtree.price_suggestion.available.all").inc();
        });
    }

    private Tuple2<VrmAttributes.Builder, HashMap<String, Consumer<SummaryAttribute>>> getAttributeBuilders() {
        final VrmAttributes.Builder vrmAttributeBuilder = new VrmAttributes.Builder();
        HashMap<String, Consumer<SummaryAttribute>> builders = HashMap.ofEntries(
                Tuple.of(VRN.getName(), vrmAttributeBuilder::setVrm),
                Tuple.of(MOTORBIKE_MAKE.getName(), vrmAttributeBuilder::setMotorbikeMake),
                Tuple.of(VEHICLE_MAKE.getName(), vrmAttributeBuilder::setMake),
                Tuple.of(VEHICLE_MODEL.getName(), vrmAttributeBuilder::setModel),
                Tuple.of(VEHICLE_BODY_TYPE.getName(), vrmAttributeBuilder::setBodyType),
                Tuple.of(VEHICLE_COLOUR.getName(), vrmAttributeBuilder::setColour),
                Tuple.of(VEHICLE_TRANSMISSION.getName(), vrmAttributeBuilder::setTransmission),
                Tuple.of(VEHICLE_FUEL_TYPE.getName(), vrmAttributeBuilder::setFuelType),
                Tuple.of(VEHICLE_REGISTRATION_YEAR.getName(), vrmAttributeBuilder::setRegistrationYear),
                Tuple.of(VEHICLE_ENGINE_SIZE.getName(), vrmAttributeBuilder::setEngineSize),
                Tuple.of(VEHICLE_DOORS.getName(), vrmAttributeBuilder::setDoors),
                Tuple.of(VEHICLE_ESTIMATED_MILEAGE.getName(), vrmAttributeBuilder::setEstimatedMileage)
        );
        return Tuple.of(vrmAttributeBuilder, builders);
    }

}
