package com.gumtree.web.seller.page.postad.service;

import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeValueMetadata;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class PhoneAttributesValidationService {

    private static final Logger LOG = LoggerFactory.getLogger(PhoneAttributesValidationService.class);

    private final CategoryModel categoryModel;
    private final CustomMetricRegistry customMetricRegistry;

    public final Long MOBILE_PHONES_CATEGORY_ID = 4660L;

    static HashSet<String> phoneAttributes = new HashSet<>();

    static {
        phoneAttributes.add("mobile_condition");
        phoneAttributes.add("mobile_colour");
        phoneAttributes.add("mobile_storage_capacity");
        phoneAttributes.add("mobile_model_apple");
        phoneAttributes.add("mobile_model_samsung");
        phoneAttributes.add("mobile_model_google");
        phoneAttributes.add("mobile_model_xiaomi");
        phoneAttributes.add("mobile_model_huawei");
    }
    @Autowired
    public PhoneAttributesValidationService(CategoryModel categoryModel, CustomMetricRegistry customMetricRegistry) {
        this.categoryModel = categoryModel;
        this.customMetricRegistry = customMetricRegistry;
    }

    public boolean isMobilePhones(Long categoryId){
        return categoryModel.isChildOrEqual(MOBILE_PHONES_CATEGORY_ID, categoryId);
    }

    /**
     * Validates if the passed phone attributes are valid for the given category.
     *
     * @param categoryId The ID of the category to check against.
     * @param attributes A map of attributes containing the phone-related properties.
     * @return true if all phone attributes are valid for the given category, false otherwise.
     */
    public boolean isValidPhoneValuePassed(Long categoryId, Map<String, String> attributes) {
        if (isMobilePhones(categoryId)) {
            // Extract common logic into a helper method
            if (!validateAttributes(phoneAttributes, attributes, categoryId)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Helper method: Validates the list of attributes.
     *
     * @param attributeList A set of attribute names to validate.
     * @param attributes A map of attributes containing the properties to be validated.
     * @param categoryId The ID of the category to check against.
     * @return true if all attributes are valid, false otherwise.
     */
    private boolean validateAttributes(Set<String> attributeList, Map<String, String> attributes, Long categoryId) {
        // If the attribute list or attributes map is null, consider it as valid
        if (attributeList == null || attributes == null) {
            return true;
        }

        attributes.replaceAll((key, value) -> StringUtils.lowerCase(value));
        // Iterate through each attribute in the list
        for (String attribute : attributeList) {
            if (attributes.containsKey(attribute)) {
                Optional<AttributeMetadata> attributeMetadata = categoryModel.findAttributesByNameForGivenCategory(attribute, categoryId);

                if (attributeMetadata.isPresent()) {
                    List<AttributeValueMetadata> listOfAttributes = attributeMetadata.get().getValues();
                    String providedAttributesValue = attributes.get(attribute);

                    // Check for null values and validate the provided attribute value
                    if (providedAttributesValue == null || !isValidAttributes(providedAttributesValue, listOfAttributes)) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    private boolean isValidAttributes(String attributeValue, List<AttributeValueMetadata> listOfAttributes) {
        if (!isMatched(attributeValue, listOfAttributes)) {
            LOG.warn("Unexpected attribute value has been passed: {}", attributeValue);
            return false;
        }
        return true;
    }

    private boolean isMatched(String attributeValue, List<AttributeValueMetadata> listOfAttributes) {
        return listOfAttributes.stream().anyMatch(attributeValueMetadata -> attributeValueMetadata.getValue().equalsIgnoreCase(attributeValue));
    }
}
