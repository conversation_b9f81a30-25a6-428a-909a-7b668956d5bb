package com.gumtree.web.seller.page.postad.validation;

import com.google.common.base.Function;
import com.google.common.collect.FluentIterable;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.gumtree.api.category.domain.Category;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Collection;
import java.util.Set;

/**
 * Validator with associated Annotation for PostAdDetail that must have 1 image or more if the category requires it.
 */
public final class MinImagesPostedValidator implements ConstraintValidator<MinImagesPostedCategory, PostAdDetail> {

    private final CategoryService categoryService;
    private final ImmutableSet<Long> categoriesWithImageRequired;
    private static final Function<Category, Long> CAT_TO_ID = new Function<Category, Long>() {
        @Override
        public Long apply(Category input) {
            return input.getId();
        }
    };

    @Autowired
    public MinImagesPostedValidator(
            CategoryService categoryService,
            @Value("#{'${gumtree.postad.validation.image.categories:2526,10442}'.split(',')}")
            Long[] categoriesWithImageRequired) {
        this.categoryService = categoryService;
        this.categoriesWithImageRequired = ImmutableSet.copyOf(categoriesWithImageRequired);
    }

    private String[] fieldList;
    private String message;

    /**
     * @param constraintAnnotation init
     */
    @Override
    public void initialize(MinImagesPostedCategory constraintAnnotation) {
        this.fieldList = constraintAnnotation.fieldList();
        this.message = constraintAnnotation.message();
    }

    @Override
    public boolean isValid(PostAdDetail postAdDetail, ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addNode(fieldList[0]).addConstraintViolation();

        Long categoryId = postAdDetail.getCategoryId();
        final Collection<Category> parentsOfCurrent = categoryService.getLevelHierarchy(categoryId).values();
        final Set<Long> intersection = Sets.intersection(categoriesWithImageRequired,
                FluentIterable.from(parentsOfCurrent).transform(CAT_TO_ID).toSet());
        final boolean categoryRequiresImage = !intersection.isEmpty();
        return !categoryRequiresImage || !postAdDetail.getImages().isEmpty();
    }

}
