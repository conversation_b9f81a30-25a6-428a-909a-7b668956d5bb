package com.gumtree.web.seller.page.postad.model;

import com.gumtree.web.seller.page.postad.model.plupload.PluploadError;

public class PostAdImageUpload extends PostAdImage {

    private PluploadError error;
    private String fileName;
    private Integer position;

    public PostAdImageUpload() {

    }

    public PostAdImageUpload(Builder builder) {
        this.error = builder.error;
        this.fileName = builder.fileName;
        if (builder.image != null) {
            setId(builder.image.getId());
            setUrl(builder.image.getUrl());
            setSize(builder.image.getSize());
            setThumbnailUrl(builder.image.getThumbnailUrl());
            setPosition(builder.position);
        } else {
            setPosition(builder.position);
        }
    }

    public PluploadError getError() {
        return error;
    }

    public String getFileName() {
        return fileName;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private PluploadError error;
        private String fileName;
        private PostAdImage image;
        private Integer position;

        private Builder() {
        }

        public Builder withError(PluploadError.Builder error) {
            this.error = error.build();
            return this;
        }

        public Builder withFileName(String fileName){
            this.fileName = fileName;
            return this;
        }

        public Builder withImage(PostAdImage image) {
            this.image = image;
            return this;
        }

        public Builder withPosition(Integer position) {
            this.position = position;
            return this;
        }

        public PostAdImageUpload build() {
            return new PostAdImageUpload(this);
        }
    }

}
