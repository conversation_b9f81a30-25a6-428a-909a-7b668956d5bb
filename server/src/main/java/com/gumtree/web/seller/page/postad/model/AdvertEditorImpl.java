package com.gumtree.web.seller.page.postad.model;

import com.google.common.base.Optional;
import com.google.common.collect.*;
import com.gumtree.api.*;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.client.executor.command.ValidateRegistrationFormApiCall;
import com.gumtree.api.client.executor.impl.AuthenticatedApiCall;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.domain.advert.PostAdvertBean;
import com.gumtree.api.domain.order.ProductNamesSearchResponse;
import com.gumtree.api.domain.user.beans.RegisterUserBean;
import com.gumtree.api.domain.user.beans.RegisterUserMethod;
import com.gumtree.bapi.AccountApi;
import com.gumtree.common.properties.GtProps;
import com.gumtree.common.util.error.ReportableResponse;
import com.gumtree.common.util.error.ReportableResponseAggregator;
import com.gumtree.config.SellerProperty;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.seller.domain.image.entity.ImageSize;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.service.category.CategoryService;
import com.gumtree.util.url.CdnImageUrlProvider;
import com.gumtree.web.common.error.mvc.ConstraintViolationErrorList;
import com.gumtree.web.security.UserLoginStatus;
import com.gumtree.web.security.UserSecurityManager;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.ajax.vrm.MotorsApiClient;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.postad.api.ValidateAdApiCall;
import com.gumtree.web.seller.page.postad.model.exception.AttributesNotFoundException;
import com.gumtree.web.seller.page.postad.model.features.FeatureBean;
import com.gumtree.web.seller.page.postad.model.location.PostcodeLookupResponse;
import com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState;
import com.gumtree.web.seller.page.postad.model.products.AnimalType;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import com.gumtree.web.seller.page.postad.service.CommonAttributesValidationService;
import com.gumtree.web.seller.page.postad.service.PetAttributesValidationService;
import com.gumtree.web.seller.page.postad.service.PhoneAttributesValidationService;
import com.gumtree.web.seller.page.postad.service.legal.LegalBean;
import com.gumtree.web.seller.page.postad.service.legal.PostAdLegalService;
import com.gumtree.web.seller.service.location.PostAdLocationService;
import com.gumtree.web.seller.service.postad.UserPostcodeLookupService;
import com.gumtree.web.seller.service.presentation.config.AttributePresentationService;
import com.gumtree.web.seller.service.pricing.PricingContextImpl;
import com.gumtree.web.seller.service.pricing.PricingService;
import com.gumtree.web.service.ContactEmailService;
import com.gumtree.web.zeno.userregistration.UserRegistrationFailZenoEvent;
import com.gumtree.zeno.core.service.ZenoService;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.Value.BOOLEAN_YES;
import static com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState.POSTCODE_UNDEFINED;
import static java.util.stream.Collectors.toList;

public final class AdvertEditorImpl extends AuthenticatedApiCall<Ad> implements AdvertEditor {

    private static final Logger LOG = LoggerFactory.getLogger(AdvertEditorImpl.class);

    private static final List<Long> FREE_RELIST_CATEGORIES
            = Stream.of(Categories.MOTORS, Categories.FOR_SALE, Categories.COMMUNITY).map(Categories::getId).collect(toList());

    private static final String IN_PATTERN = "yyyyMMdd";
    private static final String OUT_PATTERN = "dd/MM/yyyy";
    private static final String SELLER_TYPE = "seller_type";
    private static final String SELLER_TYPE_TRADE = "trade";
    private static final String SELLER_TYPE_PRIVATE = "private";
    private static final Long CAR_CATEGORY_ID = 9311L;
    private static final Long FREEBIES_CATEGORY_ID = 120L;

    private static final Integer AUTOBIZ_EXPERIMENT_LOCATION_ID = GtProps.getInt(SellerProperty.AUTOBIZ_EXPERIMENT_LOCATION_ID);

    private final UserSession userSession;
    private final UserSecurityManager userSecurityManager;
    private final UserPostcodeLookupService userPostcodeLookupService;
    private final AttributePresentationService attributePresentationService;
    private final BushfireApi bushfireApi;
    private final PostAdLocationService locationService;
    private final PostAdLegalService legalService;
    private final Validator validator;
    private final ApiCallExecutor apiCallExecutor;
    private final CategoryService categoryService;
    private final PricingService pricingService;
    private final CategoryModel categoryModel;
    private final ContactEmailService contactEmailService;
    private final MotorsApiClient motorsApiClient;
    private final ZenoService zenoService;
    private final String editorId;
    private final PostAdDetail advertDetail;
    private final CustomMetricRegistry metrics;
    private final CdnImageUrlProvider cdnImageUrlProvider;
    private final PetAttributesValidationService petAttributesValidationService;
    private final PhoneAttributesValidationService phoneAttributesValidationService;
    private final AccountApi accountApi;
    private final CommonAttributesValidationService commonAttributesValidationService;

    /**
     * Constructor with all dependencies.
     *
     * @param editorId                     - ID of editor for current advert
     * @param advertDetail                 - details of advert we are editing
     * @param bushfireApi                  - the bushfire API
     * @param userSession                  - who is logged in
     * @param attributePresentationService - attributes provider
     * @param postAdLocationService        - location info provider
     * @param apiCallExecutor              - for making api calls
     * @param validator                    - for validating beans
     * @param categoryService              - category service
     * @param pricingService
     * @param categoryModel
     * @param contactEmailService
     * @param motorsApiClient
     * @param zenoService                  - Event Logging Framework
     * @param metrics
     * @param cdnImageUrlProvider
     */
    public AdvertEditorImpl(String editorId,
                            PostAdDetail advertDetail,
                            BushfireApi bushfireApi,
                            UserSession userSession,
                            UserSecurityManager userSecurityManager,
                            UserPostcodeLookupService userPostcodeLookupService,
                            AttributePresentationService attributePresentationService,
                            PostAdLocationService postAdLocationService,
                            ApiCallExecutor apiCallExecutor,
                            Validator validator,
                            CategoryService categoryService,
                            PostAdLegalService legalService,
                            PricingService pricingService,
                            CategoryModel categoryModel,
                            ContactEmailService contactEmailService,
                            MotorsApiClient motorsApiClient,
                            ZenoService zenoService, CustomMetricRegistry metrics,
                            CdnImageUrlProvider cdnImageUrlProvider,
                            PetAttributesValidationService petAttributesValidationService,
                            PhoneAttributesValidationService phoneAttributesValidationService,
                            @Qualifier(value = "bapiContractAccountApi") AccountApi accountApi,
                            CommonAttributesValidationService commonAttributesValidationService) {
        super(userSession);

        this.editorId = editorId;
        this.advertDetail = advertDetail;
        this.bushfireApi = bushfireApi;
        this.userSession = userSession;
        this.userPostcodeLookupService = userPostcodeLookupService;
        this.attributePresentationService = attributePresentationService;
        this.userSecurityManager = userSecurityManager;
        this.locationService = postAdLocationService;
        this.apiCallExecutor = apiCallExecutor;
        this.validator = validator;
        this.categoryService = categoryService;
        this.legalService = legalService;
        this.pricingService = pricingService;
        this.categoryModel = categoryModel;
        this.contactEmailService = contactEmailService;
        this.motorsApiClient = motorsApiClient;
        this.zenoService = zenoService;
        this.metrics = metrics;
        this.cdnImageUrlProvider = cdnImageUrlProvider;
        this.petAttributesValidationService = petAttributesValidationService;
        this.phoneAttributesValidationService = phoneAttributesValidationService;
        this.accountApi = accountApi;
        this.commonAttributesValidationService = commonAttributesValidationService;
    }

    @Override
    public Long getAdvertId() {
        return advertDetail.getAdvertId();
    }

    /**
     * @return valid
     */
    @Override
    public Boolean isValid() {
        return isValidCategorySelected() && isValidLocationSelected() && !validate().isErrorResponse();
    }

    @Override
    public ReportableResponse validate() {

        Set<ConstraintViolation> violations = new HashSet<>();
        violations.addAll(validator.validate(advertDetail));

        PostAdvertBean postAdvertBean = toApiBean();
        postAdvertBean.setIp("*********"); // Set a dummy IP address for validation

        List<ReportableResponse> reportableResponses = new ArrayList<>();
        metrics.editorTimer("validateAd").record(() ->
                reportableResponses.add(apiCallExecutor.call(new ValidateAdApiCall(postAdvertBean))));


        // Only validate registration details if current user is new and unregistered.
        // If the user is new but registered then we won't be showing the reg from any more.
        if (userSession.getUserType().equals(UserLoginStatus.NEW_UNREGISTERED)) {
            ApiCallResponse<Void> apiCallResponse = metrics.editorTimer("validateRegistrationForm").record(() ->
                    apiCallExecutor.call(new ValidateRegistrationFormApiCall(toRegisterUserBean())));
            if (apiCallResponse.isErrorResponse()) {
                String emailAddress = advertDetail.getPostAdFormBean().getEmailAddress();
                zenoService.logEvent(new UserRegistrationFailZenoEvent(emailAddress));
            }
            reportableResponses.add(apiCallResponse);
        }

        ReportableResponseAggregator aggregatedResponse = new ReportableResponseAggregator(reportableResponses);
        aggregatedResponse.addErrors(new ConstraintViolationErrorList(violations));

        return aggregatedResponse;
    }

    @Override
    public boolean requiresBumpUp() {
        return advertDetail.isAdvertExpired();
    }

    public boolean supportsBumpUp() {
        return metrics.editorTimer("supportsBumpUp").record(() -> (!isCreateMode()
                && !advertDetail.isAdvertDraft()
                && !advertDetail.isRecentlyPublished()
                && advertDetail.getPublishedDate() != null
                && !advertDetail.isAdvertExpired())
                || (requiresBumpUp() && !isRelistForFree()));
    }

    @Override
    public boolean isRelistForFree() {
        return metrics.editorTimer("isRelistForFree").record(() ->
                isCategorySelectedForFreeRelist() && isInsertionFeeFree() && !isTradeSellerType());
    }

    private boolean isTradeSellerType() {
        return isSellerType(SELLER_TYPE_TRADE);
    }

    private boolean isPrivateSellerType() {
        return isSellerType(SELLER_TYPE_PRIVATE);
    }

    private boolean isSellerType(String sellerType) {
        return advertDetail.getPostAdFormBean().getAttributes().entrySet().stream()
                .anyMatch(entry -> entry.getKey().equals(SELLER_TYPE) && entry.getValue().equals(sellerType));
    }

    private boolean isCategorySelectedForFreeRelist() {
        return categoryModel.getL1CategoryFor(getCategoryId())
                .transform(Category::getId)
                .transform(FREE_RELIST_CATEGORIES::contains)
                .or(false);
    }

    @Override
    public boolean supportsChangeVisibleOnMap() {
        return advertDetail.getPostcodeState() == PostcodeSelectionState.POSTCODE_RECOGNISED;
    }

    @Override
    public boolean supportsChangeCategory() {
        return advertDetail.getAdvertId() == null;
    }

    @Override
    public boolean supportsChangeLocation() {
        return advertDetail.getAdvertId() == null;
    }

    @Override
    public boolean supportsPostToAnyLocation() {
        Optional<Category> category = categoryModel.getL1CategoryFor(getCategoryId());
        return category.isPresent() && category.get().isPostToAnyLocation();
    }

    @Override
    public Boolean supportsContactUrl() {
        Optional<Category> l1Category = categoryModel.getL1CategoryFor(getCategoryId());
        return (UserType.PRO == userSession.getUser().getType() || userSession.isSuperUser()) &&
                (Categories.JOBS.is(l1Category));
    }

    @Override
    public boolean isCreateMode() {
        return advertDetail.getAdvertId() == null;
    }

    @Override
    public boolean isEditMode() {
        return !isCreateMode();
    }

    @Override
    public String getDisplayActionVerb() {
        if (advertDetail.getAdvertId() == null) {
            return "Post";
        } else {
            return "Update";
        }
    }

    @Override
    public String getEditorId() {
        return editorId;
    }

    @Override
    public Long getCategoryId() {
        return advertDetail.getCategoryId();
    }

    @Override
    public String getPostcode() {
        return advertDetail.getPostcode();
    }

    @Override
    public Long getLocationId() {
        return advertDetail.getLocationId();
    }

    @Override
    public Long getAccountId() {
        return userSession.getSelectedAccountId();
    }

    @Override
    public User getUser() {
        return userSession.getUser();
    }

    @Override
    public PostAdFormBean getPostAdFormBean() {
        return advertDetail.getPostAdFormBean();
    }

    private void setDefaultFeatureToSevenDays(PostAdFormBean bean) {
        Map<ProductType, FeatureBean> map = bean.getFeatures();
        if (!map.containsKey(ProductType.FEATURED)) {
            FeatureBean featureBean = new FeatureBean();
            featureBean.setProductName(ProductName.FEATURE_7_DAY.name());
            featureBean.setSelected(false);
            map.put(ProductType.FEATURED, featureBean);
            bean.setFeatures(map);
        }
    }

    /**
     * @param postAdFormBean the form containing current state of advert
     */
    @Override
    public void setPostAdFormBean(PostAdFormBean postAdFormBean) {
        advertDetail.setPostAdFormBean(postAdFormBean);
        if (supportsChangeCategory() && categoryService.exists(postAdFormBean.getCategoryId())) {
            advertDetail.setCategoryId(postAdFormBean.getCategoryId());
            updateAttributes();
        }
        if (supportsChangeLocation()) {
            if (StringUtils.hasText(postAdFormBean.getPostcode())) {
                String postCode = postAdFormBean.getPostcode();
                advertDetail.setPostcodeLocation(locationService.lookupPostcode(postAdFormBean.getPostcode()));
                if (!isValidLocationSelected() && advertDetail.getPostcodeState() == POSTCODE_UNDEFINED) {
                    advertDetail.setPostCodeNotFound();
                    advertDetail.getPostAdFormBean().setPostcode(postCode);
                }
            } else if (postAdFormBean.getLocationId() != null) {
                advertDetail.setManualLocation(postAdFormBean.getLocationId());
            }
        }
        advertDetail.getPostAdFormBean().setInAutobizFlow(isInAutobizFlow());
    }

    @Override
    public void addImage(Image image, int position) {
        advertDetail.getImages().add(image);
        advertDetail.getPostAdFormBean().addImageOnProperPosition(position, image.getId());
    }

    @Override
    public void removeImage(Long imageId) {
        advertDetail.getImages().removeIf(image -> image.getId() == imageId);
        advertDetail.getPostAdFormBean().removeImage(imageId);
    }

    @Override
    public List<Image> getImages() {

        List<Long> uniqueImageIds = Lists.newLinkedList(Sets.newLinkedHashSet(getPostAdFormBean().getExistingImageIds()));
        Iterable<Image> images = advertDetail.getImages().stream().
                filter(input -> uniqueImageIds.contains(input.getId())).
                collect(toList());

        return Ordering.explicit(uniqueImageIds).onResultOf(Image::getId).sortedCopy(images);

    }

    @Override
    public PostAdvertBean toApiBean() {
        PostAdFormBean postAdFormBean = advertDetail.getPostAdFormBean();

        java.util.Optional<AnimalType> animalType = isAnimalCatgeory(postAdFormBean.getCategoryId());
        if(animalType.isPresent()){
            petAttributesValidationService.isValidBreedValuePassed(animalType.get(),
                    postAdFormBean.getCategoryId(), postAdFormBean.getAttributes());
        }

        if(!phoneAttributesValidationService.isValidPhoneValuePassed(postAdFormBean.getCategoryId(), postAdFormBean.getAttributes())){
          throw new AttributesNotFoundException("Unexpected attribute value has been passed");
        }

        if (!commonAttributesValidationService.isValidAttributeValuePassed(postAdFormBean.getCategoryId(),
                postAdFormBean.getAttributes())) {
          throw new AttributesNotFoundException("Unexpected attribute value has been passed");
        }

        PostAdvertBeanBuilder builder = new PostAdvertBeanBuilder();

        builder.accountId(userSession.getSelectedAccountId());

        builder.addAllExtendFields(postAdFormBean.getExtendFields());

        // if advert is awaiting phone verified, add status field to advert
        if(advertDetail.isAdvertAwaitingPhoneVerified()){
            builder.addExtendFields("AdvertStatus",AdStatus.AWAITING_PHONE_VERIFIED.getName());
        }

        if (postAdFormBean != null) {
            builder.title(postAdFormBean.getTitle())
                    .description(postAdFormBean.getDescription())
                    .images(getImages())
                    .mainImageId(postAdFormBean.getMainImageId())
                    .ipAddress(advertDetail.getIpAddress())
                    .threatmetrixSessionId(advertDetail.getThreatmetrixSessionId())
                    .cookie(advertDetail.getCookie());

            final FeatureBean websiteUrlBean = advertDetail.getPostAdFormBean().getFeatures().get(ProductType.WEBSITE_URL);

            boolean websiteUrlAlreadyPurchased = advertDetail.getExistingFeatures().containsKey(ProductType.WEBSITE_URL);
            boolean websiteUrlOptionSelected = websiteUrlBean != null && BooleanUtils.isTrue(websiteUrlBean.getSelected());

            if (websiteUrlOptionSelected || websiteUrlAlreadyPurchased) {
                builder.websiteUrl(postAdFormBean.getWebsiteUrl());
            } else {
                builder.websiteUrl(null);
            }

            if (StringUtils.hasText(postAdFormBean.getYoutubeLink())) {
                builder.youtubeLink(postAdFormBean.getYoutubeLink());
            }

            final boolean isPostCodeNotEmpty = org.springframework.util.StringUtils.hasText(getPostcode());
            if (isPostCodeNotEmpty) {
                builder.postcode(getPostcode());
            } else {
                builder.locationId(getLocationId());
            }
            builder.visibleOnMap(isPostCodeNotEmpty
                    && postAdFormBean.getVisibleOnMap() != null && postAdFormBean.getVisibleOnMap());
            builder.localArea(postAdFormBean.getArea());
            if (postAdFormBean.isUseEmail()) {
                builder.contactEmail(userSession.getUserType().equals(UserLoginStatus.NEW_UNREGISTERED) ? postAdFormBean.getEmailAddress()
                        : postAdFormBean.getContactEmail());
            }

            if (postAdFormBean.isUsePhone()) {
                builder.contactPhone(postAdFormBean.getContactTelephone());
            }
            builder.contactName(postAdFormBean.getContactName());
            if (postAdFormBean.isUseUrl() && supportsContactUrl()) {
                builder.contactUrl(postAdFormBean.getContactUrl());
            }

            Long categoryId = getCategoryId();
            Map<String, String> attributes;
            if (categoryHasVrmAttribute(categoryId)) {
                Map<String, String> notNullAttrs = filterOutNullAttributes(postAdFormBean.getAttributes());
                Map<String, String> normalizedAttrs = filterOutUnselectedAdditionalFeatures(categoryId, notNullAttrs);
                attributes = motorsApiClient.standardiseVehicleData(categoryId, normalizedAttrs);
            } else {
                attributes = postAdFormBean.getAttributes();
            }
            Optional<Long> hiddenCategoryId = hiddenCategoryId(attributes, categoryId);
            builder.categoryId(hiddenCategoryId.or(categoryId));

            attributes.entrySet().stream()
                    .forEach(entry ->
                            builder.attribute(entry.getKey(), entry.getValue(), categoryModel.getAttributeType(entry.getKey()).orNull()));
        }

        return builder.build();
    }

    private boolean categoryHasVrmAttribute(Long categoryId) {
        return categoryModel.findAttributesByNameForGivenCategory(CategoryConstants.Attribute.VRN.getName(), categoryId).isPresent();
    }

    @Override
    public RegisterUserBean toRegisterUserBean() {
        PostAdFormBean postAdFormBean = getPostAdFormBean();
        RegisterUserBean bean = new RegisterUserBean();
        bean.setRegisterUserMethod(RegisterUserMethod.POST_AD_FLOW);
        bean.setFirstName(postAdFormBean.getFirstName());
        bean.setLastName(postAdFormBean.getLastName());
        bean.setEmailAddress(postAdFormBean.getEmailAddress());
        bean.setConfirmedEmailAddress(postAdFormBean.getEmailAddress());
        bean.setTelephoneNumber(postAdFormBean.getTelephoneNumber());
        bean.setPassword(postAdFormBean.getPassword());
        bean.setConfirmedPassword(postAdFormBean.getPassword());
        bean.setOptInMarketing(postAdFormBean.isOptInMarketing());
        return bean;
    }

    @Override
    public PostAdDetail getAdvertDetail() {
        return advertDetail;
    }

    private List<PostAdAttribute> collectAttributes(Collection<PostAdAttributeGroup> groups) {
        List<PostAdAttribute> attributes = new ArrayList<>();
        for (PostAdAttributeGroup group : groups) {
            attributes.addAll(group.getAttributes().values());
        }
        return attributes;
    }

    @Override
    public void loadAdvert(Long advertId) {
        Ad ad = bushfireApi.advertApi().getAdvert(advertId);
        if (ad == null) {
            throw new UnsupportedOperationException("Advert with id " + advertId + " does not exists");
        } else if (!accountCanEditAd(ad.getAccountId())) {
            throw new UnsupportedOperationException("Account cannot edit advert " + advertId + " as advert account id " + ad.getAccountId() + " is different from session account id " + userSession.getSelectedAccountId());
        } else {
            load(ad);
        }
    }

    private boolean accountCanEditAd(Long accountId) {
        Long userSessionSelectedAccountId = userSession.getSelectedAccountId();
        return ObjectUtils.equals(accountId, userSessionSelectedAccountId);
    }

    private void load(Ad ad) {

        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setContactEmail(ad.getRepliesEmail());
        formBean.setContactTelephone(ad.getPhoneNumber());
        formBean.setContactName(ad.getContactName());
        formBean.setContactUrl(ad.getReplyLink());
        formBean.setTitle(ad.getTitle());
        formBean.setDescription(ad.getDescription());
        formBean.setUseEmail(ad.getRepliesEmail() != null);
        formBean.setUsePhone(ad.getPhoneNumber() != null);
        formBean.setUseUrl(ad.getReplyLink() != null);
        formBean.setYoutubeLink(ad.getYoutubeLink());
        formBean.setWebsiteUrl(ad.getWebsiteUrl());
        formBean.setArea(ad.getLocationString());
        formBean.setVisibleOnMap(ad.isVisibleOnMap());

        loadAttributes(ad, formBean);
        setDefaultFeatureToSevenDays(formBean);
        loadImages(ad, formBean);
        loadFeatures(ad, advertDetail);

        advertDetail.setPostAdFormBean(formBean);
        advertDetail.setCategoryId(ad.getCategoryId());
        if (StringUtils.hasText(ad.getPostcode())) {
            advertDetail.setPostcodeLocation(locationService.lookupPostcode(ad.getPostcode()));
        } else {
            advertDetail.setManualLocation(ad.getLocationId());
        }
        formBean.setInAutobizFlow(isInAutobizFlow());

        advertDetail.setStatus(ad.getStatus());
        advertDetail.setAdvertId(ad.getId());
        advertDetail.setRecentlyPublished(ad.isRecentlyPublished());
        advertDetail.setPublishedDate(ad.getLiveDate());
    }

    @Override
    public void initNew(Long categoryId) {
        User user = userSession.getUser();
        String preferredEmail = contactEmailService.getPreferred(user);
        PostAdFormBean formBean = new PostAdFormBean(user, preferredEmail);
        setDefaultFeatureToSevenDays(formBean);
        advertDetail.setPostAdFormBean(formBean);

        if (categoryService.exists(categoryId)) {
            advertDetail.setCategoryId(categoryId);
        }

        if (UserLoginStatus.EXISTING == userSession.getUserType()) {
            setPostcodeFromAccountPostcode();
            setDefaultAccountImage();
        }
        formBean.setInAutobizFlow(isInAutobizFlow());
    }

    private void setPostcodeFromAccountPostcode() {
        Long selectedAccountId = userSession.getSelectedAccountId();
        Optional<String> accPostcode = userPostcodeLookupService.getLastUsedPostcode(selectedAccountId);
        if (accPostcode.isPresent()) {
            advertDetail.setPostcodeLocation(locationService.lookupPostcode(accPostcode.get()));
        }
    }

    private void setDefaultAccountImage() {
        Account selectedAccount = userSecurityManager.getAccount(userSession.getSelectedAccountId(), userSession);
        if (selectedAccount != null && selectedAccount.getDefaultImage() != null) {
            Image image = new Image();
            image.setId(selectedAccount.getDefaultImage().getId());
            image.setUrl(selectedAccount.getDefaultImage().getUrl());
            image.setSize(ImageSize.MAIN.getName());
            getAdvertDetail().getImages().add(image);
            getPostAdFormBean().addImageOnProperPosition(0, image.getId());
        }
    }

    @Override
    public void populateShoppingCart(ShoppingCart shoppingCart) {
        if (isCreateMode() || advertDetail.isAdvertDraft() || advertDetail.isAdvertAwaitingPhoneVerified() || (!isInsertionFeeFree() && !requiresBumpUp())) {
            shoppingCart.addProduct(ProductName.INSERTION);
        } else if (advertDetail.isAdvertExpired()) {
            if (isRelistForFree()) {
                shoppingCart.addProduct(ProductName.INSERTION);
            } else {
                shoppingCart.addProduct(ProductName.BUMP_UP);
            }
        }

        for (FeatureBean featureBean : getPostAdFormBean().getFeatures().values()) {

            if (featureBean.getProductName() != null && Boolean.TRUE.equals(featureBean.getSelected())) {
                try {
                    shoppingCart.addProduct(ProductName.valueOf(featureBean.getProductName()));

                } catch (IllegalArgumentException ex) {

                    LOG.warn("Unrecognised product name: " + featureBean.getProductName(), ex);
                }
            }
        }
    }

    private boolean isInsertionFeeFree() {
        PricingMetadata priceInformation = metrics.editorTimer("getPriceInformation").record(() ->
                pricingService.getPriceInformation(new PricingContextImpl(this, categoryModel)));

        return priceInformation.getInsertionPrice().isFree();
    }

    private void loadAttributes(Ad ad, PostAdFormBean formBean) {
        if (ad.getAttributes() != null) {
            Map<String, String> attributes = new HashMap<>();
            for (Attribute attribute : ad.getAttributes()) {
                attributes.put(attribute.getName(), formatAttributeValue(attribute.getName(), attribute.getValue()));
            }
            formBean.setAttributes(attributes);
        }
    }

    private String formatAttributeValue(String attributeKey, String attributeValue) {
        AttributeType type = categoryModel.getAttributeType(attributeKey).orNull();

        if (type != null) {
            switch (type) {
                case CURRENCY:
                    return convertToPrice(attributeValue);
                case DATETIME:
                    return convertDate(attributeValue);
                default:
                    return attributeValue;
            }
        }
        return attributeValue;
    }


    private String convertDate(String value) {
        try {
            Date date = new SimpleDateFormat(IN_PATTERN).parse(value.trim());
            SimpleDateFormat df = new SimpleDateFormat(OUT_PATTERN);
            return df.format(date);
        } catch (Exception e) {
            return "";
        }
    }

    private String convertToPrice(String value) {
        try {
            return new BigDecimal(value).divide(new BigDecimal(100)).toString();
        } catch (Exception ex) {
            return "";
        }
    }

    private void loadFeatures(Ad ad, PostAdDetail adDetail) {
        ProductNamesSearchResponse response = metrics.editorTimer("searchPaidProductNamesByAccountId").record(() ->
                bushfireApi.orderApi().searchPaidProductNamesByAccountId(ad.getId()));
        if (response != null) {
            for (ProductName productName : response.getProductNames()) {
                adDetail.addExistingFeature(ProductType.getType(productName), null);
            }
        }

        if (ad.getFeatures() != null) {
            for (AdFeature feature : ad.getFeatures()) {
                adDetail.addExistingFeature(ProductType.getType(feature.getProductName()), feature.getEndDate());
            }
        }
    }

    private void loadImages(Ad ad, PostAdFormBean formBean) {
        if (ad.getImages() != null) {
            List<Image> images = new ArrayList<>();
            List<Long> imagIds = Lists.newLinkedList();

            for (Map<String, LegacyImage> legacyImageMap : ad.getImages()) {

                if (legacyImageMap != null && legacyImageMap.size() > 0) {
                    // This is pulling the first item in the map from the BAPI response, which is
                    // of type 'preview'.
                    LegacyImage legacyImage = new ArrayList<>(legacyImageMap.values()).get(0);
                    Long legacyImageId = legacyImage.getId();

                    // When creating the Image, we override the imageVariant in getUrlFromLegacyImage.
                    // Having BAPI determine what should be displayed on the UI is pretty brittle, but
                    // relying on the order of keys in an object is even worse.
                    // While BAPI should supply the base image URL, Seller should be determining its own
                    // variants.
                    Image image = new Image();
                    image.setId(legacyImageId);
                    image.setUrl(getUrlFromLegacyImage(legacyImage));

                    images.add(image);
                    imagIds.add(legacyImageId);
                }
            }

            advertDetail.setImages(images);
            formBean.setImageIds(imagIds);
            formBean.setMainImageId(ad.getMainImageId());
        }
    }

    /**
     * Retrieves the URL from the legacy image.
     *
     * If the image is a CloudFlare URL, the variant is changed to be of type ImageSize.MAIN.
     *
     * If the image is not a CloudFlare URL, the URL is returned without any manipulation.
     *
     * @param legacyImage
     * @return
     */
    private String getUrlFromLegacyImage(LegacyImage legacyImage) {
        if (cdnImageUrlProvider.isCloudFlareUrl(legacyImage.getUrl())) {
            return cdnImageUrlProvider.buildCFImageUrl(legacyImage.getUrl(), ImageSize.MAIN.getId());
        }

        return legacyImage.getUrl();
    }

    /**
     * "Apply" the current category's attributes to the map. The logic will preserve any
     * attributes (and their values) that are still relevant for the new category. It will, though, remove any
     * attributes that are no longer relevant for the new category. Default attribute values for a specified category
     * will be re-applied where applicable, even if the attribute is already defined in the map, e.g. when switching
     * category from "Audi" => "BMW", the "Vehicle Make" attribute will be updated to reflect the new category, even
     * if it was already defined.
     */
    private void updateAttributes() {
        PostAdFormBean postAdFormBean = getPostAdFormBean();
        Long categoryId = getCategoryId();
        Optional<Category> category = categoryService.getById(categoryId);

        Map<String, String> formAdAttributes = postAdFormBean.getAttributes();
        Map<String, String> newAttributes = new HashMap<>();

        Collection<PostAdAttributeGroup> attributeGroups =
                attributePresentationService.loadAttributeGroups(categoryId, postAdFormBean.getAttributes());
        List<PostAdAttribute> categoryAttributes = collectAttributes(attributeGroups);
        for (PostAdAttribute attribute : categoryAttributes) {

            String attributeId = attribute.getId();
            String attributeValue = formAdAttributes.get(attributeId);
            newAttributes.put(attributeId, attributeValue);
        }
        MapDifference<String, String> attributesNotInPresentationJson = Maps.difference(formAdAttributes, newAttributes);
        Map<String, String> attributesMissingFromPresentationJson = attributesNotInPresentationJson.entriesOnlyOnLeft();
        for (Map.Entry<String, String> attribute : attributesMissingFromPresentationJson.entrySet()) {
            if (category.isPresent() && category.get().findAttribute(attribute.getKey()).isPresent()) {
                newAttributes.put(attribute.getKey(), attribute.getValue());
            }
        }

        // if category is Freebies, we should add 0 price to attributes to be able to filter
        // adverts by 0 price in refinement panel
        addZeroPriceAttributeForFreebie(categoryId, newAttributes);

        postAdFormBean.setAttributes(newAttributes);
    }

    private void addZeroPriceAttributeForFreebie(Long categoryId, Map<String, String> newAttributes) {
        String priceAttributeName = CategoryConstants.Attribute.PRICE.getName();
        if (FREEBIES_CATEGORY_ID.equals(categoryId)
                && categoryModel.isSupportedAttribute(priceAttributeName, categoryId)) {
            newAttributes.put(priceAttributeName, "0");
        }
    }

    @Override
    public Ad execute(BushfireApi api) {
        Long advertId = advertDetail.getAdvertId();

        if (advertId == null || advertId == 0L) {
            // We must be posting a new ad!
            Ad ad = metrics.editorTimer("createAd").record(() -> api.create(AdvertApi.class, getApiKey()).postAd(toApiBean()));

            return ad;

        } else {
            // We must be updating an ad!
            return metrics.editorTimer("updateAd").record(() -> api.create(AdvertApi.class, getApiKey()).updateAd(advertId, toApiBean()));
        }
    }

    @Override
    public boolean isDraftMode() {
        return advertDetail.isAdvertDraft();
    }

    @Override
    public Optional<LegalBean> getLegalRequirements() {
        LegalBean legal = legalService.getLegal(getCategoryId());
        return Optional.fromNullable(legalRequired(legal) ? legal : null);
    }

    private boolean legalRequired(LegalBean legal) {
        return isCreateMode() && legal.isRequired() && Boolean.TRUE != getPostAdFormBean().isTermsAgreed();
    }

    @Override
    public Boolean isValidCategorySelected() {
        if (isEditMode()) {
            return true;
        }
        Optional<Category> category = categoryService.getById(getCategoryId());
        return category.isPresent() && category.get().isAdPostingPermitted();
    }

    @Override
    public Boolean isValidLocationSelected() {
        if (isEditMode() || postcodeRecognized()) {
            return true;
        }
        Location location = locationService.get(getLocationId());
        if (location == null) {
            return false;
        }
        if (supportsPostToAnyLocation()) {
            return !locationService.isCounty(location);
        }
        return !locationService.hasZoomIn(location);
    }

    private boolean postcodeRecognized() {
        return advertDetail.getPostcodeState() == PostcodeSelectionState.POSTCODE_RECOGNISED;
    }

    @Override
    public Boolean showInsertionPrice() {
        return isCreateMode() || isDraftMode();
    }

    @Override
    public Boolean isUserLoggedIn() {
        return getUser().getId() != null;
    }

    @Override
    public void refreshPreferredContactDetailsOfNonProUser() {
        if (isUserLoggedIn() && !userSession.isProUser()) {
            User user = bushfireApi.userApi().getUser(userSession.getUsername());
            final String preferredEmail = contactEmailService.getPreferred(user);

            PostAdFormBean postAdFormBean = advertDetail.getPostAdFormBean();
            if (getAdvertId() != null) {
                if (postAdFormBean.getPreviousContactName() == null && !user.getFirstName().equals(postAdFormBean.getContactName())) {
                    postAdFormBean.setPreviousContactName(postAdFormBean.getContactName());
                }

                if (postAdFormBean.getPreviousContactEmail() == null && !preferredEmail.equals(postAdFormBean.getContactEmail())) {
                    postAdFormBean.setPreviousContactEmail(postAdFormBean.getContactEmail());
                }
            } else {
                postAdFormBean.setPreviousContactName(null);
                postAdFormBean.setPreviousContactEmail(null);
            }
            postAdFormBean.setContactName(user.getFirstName());
            postAdFormBean.setContactEmail(preferredEmail);
        }
    }

    @Override
    public void setCheckoutVariant(String checkoutVariationId) {
        advertDetail.getPostAdFormBean().setCheckoutVariationId(checkoutVariationId);
    }

    /**
     * Some categories have been downgraded and "hidden" and replaced by an attribute. Car makes is an example.
     * Buyer and seller web applications will no longer intentionally use these hidden categories.
     * However, other platform components depend on them, to ensure the platform continues to work seamlessly,
     * ads will be saved with the hidden Category ID. A lookup is performed on attributes to identify when the
     * substitution is necessary. In the case of cars, the vehicle_make attribute will contain the reverse lookup
     * detail.
     *
     * @param attributes
     * @param categoryId
     * @return
     */
    private Optional<Long> hiddenCategoryId(Map<String, String> attributes, Long categoryId) {

        Optional<Category> oCategory = categoryModel.getCategory(categoryId);

        if (oCategory.isPresent()) {
            return hiddenCategoryId(attributes, oCategory.get());
        }

        return Optional.absent();
    }

    private Optional<Long> hiddenCategoryId(Map<String, String> attributes, Category category) {

        if (isHiddenLeaf(category) || isNonLeafWithNoVisibleChildren(category)) {
            // ok, need to find hidden category from attributes
            for (String attrName : attributes.keySet()) {
                Optional<AttributeMetadata> attrMeta = categoryModel.findAttributesByNameForGivenCategory(attrName, category.getId());

                if (attrMeta.isPresent()) {
                    String attributeValue = attributes.get(attrName);
                    Optional<Long> associatedCategoryId = attrMeta.get().getAssociatedCategoryId(attributeValue);
                    if (associatedCategoryId.isPresent()) {
                        return Optional.of(associatedCategoryId.get());
                    }
                }
            }
        }

        return Optional.absent();
    }

    private boolean isHiddenLeaf(Category category) {
        return category.isLeaf() && category.isHidden();
    }

    private boolean isNonLeafWithNoVisibleChildren(Category category) {
        return !category.isLeaf() && category.visibleChildCategories().isEmpty();
    }

    private Map<String, String> filterOutUnselectedAdditionalFeatures(Long categoryId, Map<String, String> attributes) {
        return attributes.entrySet().stream()
                .filter(kv -> {
                    Optional<AttributeMetadata> attr = categoryModel.findAttributesByNameForGivenCategory(kv.getKey(), categoryId);
                    if (attr.isPresent() && AttributeType.BOOLEAN == attr.get().getType() && !attr.get().isRequired()) {
                        return BOOLEAN_YES.getValue().equals(kv.getValue());
                    }
                    return true;
                })
                .collect(HashMap::new, (m, v) -> m.put(v.getKey(), v.getValue()), HashMap::putAll);
    }

    private Map<String, String> filterOutNullAttributes(Map<String, String> attributes) {
        return attributes.entrySet().stream()
                .filter(kv -> !StringUtils.isEmpty(kv.getValue()))
                .collect(HashMap::new, (m, v) -> m.put(v.getKey(), v.getValue()), HashMap::putAll);
    }

    private Location getCurrentLocation() {
        Long locationId = getLocationId();
        if (locationId == null) {
            String postcode = getPostcode();
            if (postcode != null) {
                PostcodeLookupResponse response = locationService.lookupPostcode(postcode);
                if (response != null && response.getState() == PostcodeSelectionState.POSTCODE_RECOGNISED) {
                    locationId = response.getLocationId();
                }
            }
        }

        return locationService.get(locationId);
    }

    private Boolean isInAutobizFlow() {
        if (isPrivateSellerType()) {

            Location location = getCurrentLocation();
            if (location != null && locationService.isInArea(location, AUTOBIZ_EXPERIMENT_LOCATION_ID)) {

                Long categoryId = getCategoryId();

                if (categoryId != null) {
                    if (categoryId.equals(CAR_CATEGORY_ID)) {
                        return true;
                    } else {
                        Optional<Category> carCategory = categoryService.getById(CAR_CATEGORY_ID);
                        Optional<Category> currentCategory = categoryService.getById(categoryId);

                        return (currentCategory.isPresent() && carCategory.isPresent() &&
                                categoryService.isChild(currentCategory.get(), carCategory.get()));
                    }
                }
            }
        }
        return false;
    }


    private java.util.Optional<AnimalType> isAnimalCatgeory(Long categoryId) {
        return AnimalType.fromCategoryId(categoryId);
    }
}
