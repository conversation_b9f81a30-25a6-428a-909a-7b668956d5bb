package com.gumtree.web.seller.page.postad.controller.steps;

import com.google.common.base.Optional;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.common.util.error.ReportableResponse;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.security.UserLoginStatus;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.seller.page.manageads.metric.AdvertValidationMetric;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.postad.controller.PostAdSubmitController;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.CategorySpecificPostAdFormPanels;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdFormPanel;
import com.gumtree.web.seller.page.postad.model.PostAdSubmitModel;
import com.gumtree.web.seller.page.postad.model.PriceGuidance;
import com.gumtree.web.seller.page.postad.model.PriceGuidanceContext;
import com.gumtree.web.seller.page.postad.model.features.FeatureBean;
import com.gumtree.web.seller.page.postad.model.path.PostAdBumpupForPhoneVerifyPath;
import com.gumtree.web.seller.page.postad.model.path.PostAdBumpupPath;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.page.postad.model.products.ProductPrice;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import com.gumtree.web.seller.page.postad.service.PostAdFormDescriptionHintService;
import com.gumtree.web.seller.service.postad.priceguidance.PriceGuidanceService;
import com.gumtree.web.seller.service.presentation.config.AttributePresentationService;
import com.gumtree.web.seller.service.pricing.PricingContextImpl;
import com.gumtree.web.seller.service.pricing.PricingService;
import com.gumtree.web.service.ContactEmailService;
import com.gumtree.web.storage.ratelimit.RateLimiter;
import com.gumtree.web.zeno.event.postad.PostAdFreeAttemptEvent;
import com.gumtree.web.zeno.event.postad.PostAdPaidAttemptEvent;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationBegin;
import com.gumtree.zeno.core.service.ZenoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.SELLER_TYPE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VRN;
import static com.gumtree.web.seller.page.postad.service.PostAdFormDescriptionHintService.HintSection.DESCRIPTION;

@Component
public class LastPostAdStep implements PostAdStep {
    public static final Integer ORDER = ImagesPostAdStep.ORDER + 1;
    private static final Logger log = LoggerFactory.getLogger(LastPostAdStep.class);

    @Autowired
    private ZenoService zenoService;

    @Autowired
    private PricingService pricingService;

    @Autowired
    private PriceGuidanceService priceGuidanceService;

    @Autowired
    private ErrorMessageResolver messageResolver;

    @Autowired
    private UserSession authenticatedUserSession;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private AttributePresentationService attributePresentationService;

    @Autowired
    private PostAdFormDescriptionHintService descriptionHintService;

    @Autowired
    @Qualifier("postAdRateLimiter")
    private RateLimiter rateLimiter;

    @Autowired
    private ContactEmailService contactEmailService;

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private CustomMetricRegistry metrics;

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    public boolean execute(PostAdSubmitModel.Builder model, AdvertEditor editor) {
        if (Boolean.TRUE == editor.getPostAdFormBean().isSubmitForm()) {
            onFormSubmitted(model, editor);
        }

        List<PostAdFormPanel> panels = getPanels(editor);
        addCommonModelAttributes(model, editor);

        Optional<AttributeMetadata> sellerTypeAttribute =
                categoryService.getCategoryModel().findAttributesByNameForGivenCategory(SELLER_TYPE.getName(), editor.getCategoryId());

        if (!sellerTypeAttribute.isPresent() || !sellerTypeAttribute.get().isPriceSensitive() || model.isSellerTypeSelected()) {
            if (model.hasBrands()) {
                int index = panels.indexOf(PostAdFormPanel.DESCRIPTION);
                if (index < 0) {
                    index = panels.size();
                }
                panels.add(index, PostAdFormPanel.BRANDS);
            }
            model.addPanels(panels);
        }

        if (panels.contains(PostAdFormPanel.REGISTRATION) && !model.hasGaEvents()) {
            model.withGaEvents(Lists.newArrayList(UserRegistrationBegin.class.getSimpleName()));
        }

        return true;
    }

    private void onFormSubmitted(PostAdSubmitModel.Builder builder, AdvertEditor editor) {
        PostAdFormBean postAdFormBean = editor.getPostAdFormBean();
        //Store the username in session if it is a new user registering through the post ad flow
        if (authenticatedUserSession.getUserType().equals(UserLoginStatus.NEW_UNREGISTERED)) {
            storeUserNameFromRegistrationFormInSession(postAdFormBean.getEmailAddress());
        }
        ReportableResponse apiResponse = editor.validate();
        if (apiResponse.isErrorResponse()) {
            List<String> gaEvents = (List<String>) zenoService.getRequestEvents()
                    .stream()
                    .map(e -> e.getClass().getSimpleName())
                    .collect(Collectors.toList());
            builder.withGaEvents(gaEvents);
            postAdFormBean.populateErrors(apiResponse, "postad.global.error", messageResolver);
            metrics.counter(AdvertValidationMetric.ADVERT_VALIDATION.getName(), "result", AdvertValidationMetric.Status.FAILURE.name()).increment();
        } else if (Boolean.TRUE == postAdFormBean.isSubmitForm()) {
            logZenoEvent(editor);
            rateLimiter.incRateCounter(authenticatedUserSession.getUsername());
            if (Boolean.TRUE.equals(postAdFormBean.getUnknownProperties(PostAdSubmitController.IS_PHONE_VERIFICATION_MANDATORY))) {
                builder.redirectTo(new PostAdBumpupForPhoneVerifyPath(editor.getEditorId()));
            } else {
                builder.redirectTo(new PostAdBumpupPath(editor.getEditorId()));
            }
        }
        metrics.counter(AdvertValidationMetric.ADVERT_VALIDATION.getName(), "result", AdvertValidationMetric.Status.SUCCESS.name()).increment();
    }

    private void storeUserNameFromRegistrationFormInSession(String emailAddress) {
        loginUtils.storeNewUserEmailAddressInSession(emailAddress);
    }

    private void logZenoEvent(AdvertEditor editor) {
        PricingContextImpl pricingContext = new PricingContextImpl(editor, categoryService.getCategoryModel());
        if (pricingService.getPriceInformation(pricingContext).getInsertionPrice().isFree()) {
            zenoService.logEvent(editor, null, PostAdFreeAttemptEvent.class);
        } else {
            zenoService.logEvent(editor, null, PostAdPaidAttemptEvent.class);
        }
    }

    private List<PostAdFormPanel> getPanels(AdvertEditor editor) {
        List<PostAdFormPanel> orderedPanels = new ArrayList<>();
        if (editor.isCreateMode()) {
            orderedPanels.add(PostAdFormPanel.INSERTION);
        }

        Map<String, String> formAttributes = editor.getPostAdFormBean().getAttributes();
        CategorySpecificPostAdFormPanels categorySpecificPostAdFormPanels =
                attributePresentationService.loadPrioritisedCategorySpecificFormPanels(editor.getCategoryId(), formAttributes);

        orderedPanels.addAll(categorySpecificPostAdFormPanels.getHighPriorityPanels());

        boolean isCarsCateogry = isCarsCategory(editor);
        boolean shouldStartInAdditionalFeatures = editor.isCreateMode();
        boolean alreadyStartedInInAdditionalFeaturesExp = sellerStartedPostingWithAdditionalFeatures(editor);
        if (isCarsCateogry && (shouldStartInAdditionalFeatures || alreadyStartedInInAdditionalFeaturesExp)) {
            setPanelsWithAdditionalFeatures(orderedPanels, categorySpecificPostAdFormPanels, editor);
        } else {
            setPanelByExperimentVariant(orderedPanels, categorySpecificPostAdFormPanels, editor);
        }

        orderedPanels.add(PostAdFormPanel.OVERALL_PRICE);
        orderedPanels.add(PostAdFormPanel.CONFIRMATION);
        return orderedPanels;
    }

    private boolean isCarsCategory(AdvertEditor editor) {
        return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                .anyMatch(category -> CategoryConstants.CARS_ID.equals(category.getId()));
    }

    private boolean sellerStartedPostingWithAdditionalFeatures(AdvertEditor editor) {
        Optional<List<AttributeMetadata>> attributes = categoryService.getCategoryModel().getCategoryAttributes(editor.getCategoryId());
        if (!attributes.isPresent()) {
            return false;
        }

        return attributes.get().stream()
                .filter(attr -> AttributeType.BOOLEAN == attr.getType() && !attr.isRequired())
                .anyMatch(attr -> editor.getPostAdFormBean().getAttributes().get(attr.getName()) != null);
    }

    private void setRegistrationAndContactDetailsPanels(List<PostAdFormPanel> orderedPanels, AdvertEditor editor) {
        if (authenticatedUserSession.getUserType().equals(UserLoginStatus.NEW_UNREGISTERED)) {
            orderedPanels.add(PostAdFormPanel.REGISTRATION);
        }
        if (editor.isUserLoggedIn() && !authenticatedUserSession.isProUser()) {
            orderedPanels.add(PostAdFormPanel.CONTACT_DETAILS);
        }
        if (authenticatedUserSession.isProUser()) {
            orderedPanels.add(PostAdFormPanel.CONTACT_DETAILS_PRO);
        }
    }

    private void setBottomPanels(List<PostAdFormPanel> orderedPanels, AdvertEditor editor) {

        if (editor.isCreateMode() || editor.isDraftMode() || editor.isEditMode()) {
            orderedPanels.add(PostAdFormPanel.BUMP);
        }

        setRegistrationAndContactDetailsPanels(orderedPanels, editor);
    }

    private void setPanelsWithAdditionalFeatures(List<PostAdFormPanel> orderedPanels,
                                                 CategorySpecificPostAdFormPanels categorySpecificPostAdFormPanels,
                                                 AdvertEditor editor) {
        orderedPanels.add(PostAdFormPanel.AD_TITLE);
        orderedPanels.add(PostAdFormPanel.IMAGES);
        orderedPanels.add(PostAdFormPanel.ADDITIONAL_FEATURES);
        orderedPanels.add(PostAdFormPanel.DESCRIPTION);

        orderedPanels.addAll(categorySpecificPostAdFormPanels.getLowPriorityPanels());

        orderedPanels.add(PostAdFormPanel.WEBSITE_LINK);
        if (editor.isCreateMode() || editor.isDraftMode() || editor.isEditMode()) {
            orderedPanels.add(PostAdFormPanel.BUMP);
        }

        setRegistrationAndContactDetailsPanels(orderedPanels, editor);
    }

    private void setPanelByExperimentVariant(List<PostAdFormPanel> orderedPanels,
                                             CategorySpecificPostAdFormPanels categorySpecificPostAdFormPanels,
                                             AdvertEditor editor) {
        orderedPanels.add(PostAdFormPanel.AD_TITLE);
        orderedPanels.add(PostAdFormPanel.IMAGES);
        orderedPanels.add(PostAdFormPanel.DESCRIPTION);

        orderedPanels.addAll(categorySpecificPostAdFormPanels.getLowPriorityPanels());

        orderedPanels.add(PostAdFormPanel.WEBSITE_LINK);
        setBottomPanels(orderedPanels, editor);
    }

    private void addCommonModelAttributes(PostAdSubmitModel.Builder builder, AdvertEditor editor) {
        Map<String, String> attributes = editor.getPostAdFormBean().getAttributes();
        PricingMetadata pricingMetadata =
                pricingService.getPriceInformation(new PricingContextImpl(editor, categoryService.getCategoryModel()));
        builder.withPricingMetadata(pricingMetadata);
        builder.withShowInsertionPrice(editor.showInsertionPrice());
        builder.withTotalPrice(getTotalPrice(pricingMetadata, editor));
        builder.withPanelAttributes(
                attributePresentationService.loadAttributeGroups(editor.getCategoryId(), attributes));

        builder.withAdvertId(editor.getAdvertId());

        builder.withDescriptionHint(
                descriptionHintService.getHint(DESCRIPTION, editor.getCategoryId(), editor.getLocationId()));

        builder.supportsContactUrl(editor.supportsContactUrl());

        builder.withContactEmails(authenticatedUserSession.getUserType().equals(UserLoginStatus.NEW_UNREGISTERED) ?
                ImmutableList.of(editor.getPostAdFormBean().getEmailAddress()) : contactEmailService.getForPosting(editor.getUser()));
        addPriceGuidance(builder, attributes, editor.getCategoryId());
    }

    private void addPriceGuidance(PostAdSubmitModel.Builder builder, Map<String, String> attributes, Long categoryId) {
        java.util.Optional<String> carVRNNotBlank = java.util.Optional.ofNullable(attributes.get(VRN.getName()))
                .filter(vrn -> !vrn.isEmpty());

        if (carVRNNotBlank.isPresent()) {
            java.util.Optional<PriceGuidance> priceGuidance =
                    priceGuidanceService.getForCarAd(carVRNNotBlank.get(), categoryId)
                            .map(PriceGuidanceContext::getPriceGuidance);
            builder.withPriceGuidance(priceGuidance);
        }
    }

    private BigDecimal getTotalPrice(PricingMetadata pricingMetadata, AdvertEditor editor) {

        BigDecimal total = pricingMetadata.getInsertionPrice().getPrice();
        if (editor.isRelistForFree()) {
            total = BigDecimal.ZERO;
        } else if (editor.requiresBumpUp() && !editor.showInsertionPrice()) {
            total = pricingMetadata.getBumpUpPrice().getPrice();
        }

        for (Map.Entry<ProductType, FeatureBean> feature : editor.getPostAdFormBean().getFeatures().entrySet()) {
            if (feature.getValue().getSelected() == Boolean.TRUE) {
                ProductType type = feature.getKey();
                String productName = feature.getValue().getProductName();
                ProductPrice featurePrice = pricingMetadata.getFeaturePrice(type, productName);
                if (featurePrice != null) {
                    total = total.add(featurePrice.getPrice());
                }
            }
        }
        return total;
    }
}
