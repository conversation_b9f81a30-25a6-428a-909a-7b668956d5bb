package com.gumtree.web.seller.page.postad.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.PageContextSellerController;
import com.gumtree.web.seller.service.PostAdWorkspace;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class BasePostAdController extends PageContextSellerController<Void> {

    protected static final String EDITOR_ID_PATH_VARIABLE = "editorId";
    protected static final String EDITOR_ID_REPLACEMENT = "\\{editorId\\}";

    private final PostAdWorkspace postAdWorkspace;

    protected final UserSession authenticatedUserSession;

    @Autowired
    public BasePostAdController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                UrlScheme urlScheme, PostAdWorkspace postAdWorkspace,
                                UserSession authenticatedUserSession, CategoryService categoryService,
                                GumtreePageContext<Void> pageContext, LocationService locationService,
                                UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, pageContext,
                categoryService, locationService, userSessionService);
        this.postAdWorkspace = postAdWorkspace;
        this.authenticatedUserSession = authenticatedUserSession;
    }

    protected final PostAdWorkspace getPostAdWorkspace() {
        return postAdWorkspace;
    }

    protected final UserSession getAuthenticatedUserSession() {
        return authenticatedUserSession;
    }

    protected final Long getAccountId() {
        return authenticatedUserSession.getSelectedAccountId();
    }

}
