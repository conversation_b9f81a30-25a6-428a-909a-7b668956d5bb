package com.gumtree.web.seller.page.postad.model.location;

import com.gumtree.common.util.error.SimpleError;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Range of postcode selection states.
 */
public enum PostcodeSelectionState {

    POSTCODE_UNDEFINED() {

        @Override
        public List<SimpleError> generateErrorsForState() {
            return new ArrayList<SimpleError>();
        }
    },
    POSTCODE_RECOGNISED() {

        @Override
        public List<SimpleError> generateErrorsForState() {
            return new ArrayList<SimpleError>();
        }
    },
    OUTCODE_RECOGNISED() {

        @Override
        public List<SimpleError> generateErrorsForState() {
            return Arrays.asList(new SimpleError("postcode", "postad.postcode.outcode_only", null));
        }
    },
    POSTCODE_MISSING() {

        @Override
        public List<SimpleError> generateErrorsForState() {
            return Arrays.asList(new SimpleError("postcode", "postad.postcode.missing", null));
        }
    },
    POSTCODE_NOT_FOUND() {

        @Override
        public List<SimpleError> generateErrorsForState() {
            return Arrays.asList(new SimpleError("postcode", "postad.postcode.not_found", null));
        }
    },
    POSTCODE_INVALID() {

        @Override
        public List<SimpleError> generateErrorsForState() {
            return Arrays.asList(new SimpleError("postcode", "postad.postcode.invalid", null));
        }
    };

    /**
     * @return errors for this state.
     */
    public abstract List<SimpleError> generateErrorsForState();
}
