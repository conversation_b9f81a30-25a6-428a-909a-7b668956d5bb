package com.gumtree.web.seller.page.postad.converter;

import com.gumtree.api.Image;
import com.gumtree.seller.domain.image.entity.ImageSize;
import com.gumtree.util.url.CdnImageUrlProvider;
import com.gumtree.web.seller.page.postad.model.PostAdImage;
import com.gumtree.web.service.images.secure.SecureImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Converts an Image into a PostAdImage
 */
@Component
public class PostAdImageConverterImpl implements PostAdImageConverter {

    private  final CdnImageUrlProvider cdnImageUrlProvider;
    @Autowired
    public PostAdImageConverterImpl(CdnImageUrlProvider cdnImageUrlProvider) {
        this.cdnImageUrlProvider = cdnImageUrlProvider;
    }

    @Override
    public final PostAdImage convertImageToPostAdImage(Image image) {
        PostAdImage.Builder postAdImageBuilder = new PostAdImage.Builder()
                                .id(image.getId())
                                .size(image.getSize())
                                .url(image.getUrl());

        postAdImageBuilder.thumbnailUrl(cdnImageUrlProvider.getSecureImageUrl(image.getThumbnailUrl(),
                ImageSize.MINITHUMB.getId()));

        return postAdImageBuilder.build();
    }
}
