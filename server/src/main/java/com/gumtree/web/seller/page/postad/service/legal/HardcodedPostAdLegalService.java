package com.gumtree.web.seller.page.postad.service.legal;

import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Properties;

@Service
public class HardcodedPostAdLegalService implements PostAdLegalService {

    private final CategoryModel categoryModel;
    private final Properties messages;

    @Autowired
    public HardcodedPostAdLegalService(CategoryModel categoryModel) throws IOException {
        this.categoryModel = categoryModel;
        this.messages = loadMessages();
    }

    private Properties loadMessages() throws IOException {
        Properties properties = new Properties();
        properties.load(getClass().getResourceAsStream("HardcodedPostAsLegalService.properties"));
        return properties;
    }

    @Override
    public LegalBean getLegal(Long categoryId) {
        Optional<Category> category = categoryModel.getCategory(categoryId);
        LegalBean.Builder beanBuilder = LegalBean.builder();

        if(category.isPresent()) {
            if (isUnderCategory(CategoryConstants.MASSAGE_SERVICES_ID, category.get())) {
                beanBuilder.withMessage(messages.getProperty("massages.message"));
                beanBuilder.withLabel(messages.getProperty("massages.label"));
            }
        }
        return beanBuilder.build();
    }

    private boolean isUnderCategory(Long parentId, Category submittedCategory) {
        return parentId.equals(submittedCategory.getId()) ||
                categoryModel.isChild(parentId, submittedCategory.getId());
    }

}
