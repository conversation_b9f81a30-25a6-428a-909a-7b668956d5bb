package com.gumtree.web.seller.page.postad.controller;

import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.security.phone.number.authenticator.model.AuthenticationStatus;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.ip.RemoteIP;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.security.UserLoginStatus;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.postad.common.AppVersionUtils;
import com.gumtree.web.seller.page.postad.controller.steps.CategoryAttributeSelectPostAdStep;
import com.gumtree.web.seller.page.postad.controller.steps.PostAdStep;
import com.gumtree.web.seller.page.postad.converter.PostAdImageConverter;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdSubmitModel;
import com.gumtree.web.seller.page.postad.model.path.PostAdStatePath;
import com.gumtree.web.seller.page.postad.service.PostAdFormDescriptionHintService;
import com.gumtree.web.seller.service.PostAdWorkspace;
import com.gumtree.web.seller.service.phoneverify.PhoneVerifyService;
import com.gumtree.web.seller.service.presentation.config.AttributePresentationService;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Controller for handling post ad page state redirects.
 * <p>
 * If category id not in session, redirect => Category Selection Page If category id in session but valid location not
 * set, redirect => Location Selection Page If category id in session and valid location set, redirect => Form page
 */
@Slf4j
@Controller
@RequestMapping(PostAdStatePath.MAPPING)
@GumtreePage(PageType.PostAd)
public final class PostAdSubmitController extends BasePostAdController implements InitializingBean {

    @Autowired
    private final List<PostAdStep> postAdSteps = new ArrayList<>();

    @Autowired
    private final CustomMetricRegistry metrics;
    private final PhoneVerifyService phoneVerifyService;
    private final AttributePresentationService attributePresentationService;
    private PostAdImageConverter postAdImageConverter;
    private PostAdFormDescriptionHintService descriptionHintService;

    public static final String IS_PHONE_VERIFICATION_MANDATORY = "isPhoneVerificationMandatory";

    /**
     * Constructor.
     */
    @Autowired
    public PostAdSubmitController(
            CookieResolver cookieResolver,
            CategoryModel categoryModel,
            ApiCallExecutor apiCallExecutor,
            ErrorMessageResolver messageResolver,
            UrlScheme urlScheme,
            PostAdWorkspace postAdWorkspace,
            UserSession userSession,
            CategoryService categoryService,
            GumtreePageContext pageContext,
            LocationService locationService,
            UserSessionService userSessionService, CustomMetricRegistry metrics,
            PhoneVerifyService phoneVerifyService, AttributePresentationService attributePresentationService,
            PostAdImageConverter postAdImageConverter,PostAdFormDescriptionHintService descriptionHintService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, postAdWorkspace, userSession,
                categoryService, pageContext, locationService, userSessionService);
        this.metrics = metrics;
        this.phoneVerifyService = phoneVerifyService;
        this.attributePresentationService = attributePresentationService;
        this.postAdImageConverter = postAdImageConverter;
        this.descriptionHintService = descriptionHintService;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Collections.sort(postAdSteps, (right, left) -> right.getOrder() - left.getOrder());
    }

    /**
     * main submit method
     */
    @RequestMapping(
            value = PostAdStatePath.MAPPING_EDITOR,
            method = RequestMethod.POST,
            headers = {"content-type=application/json"}
    )
    @ResponseBody
    public final PostAdSubmitModel postAdvert(
            @RequestBody PostAdFormBeanWithAction postAdFormBeanWithAction,
            @PathVariable(value = EDITOR_ID_PATH_VARIABLE) String editorId,
            RemoteIP remoteIP, HttpServletRequest request) {

        long startUpdate = System.currentTimeMillis();
        AdvertEditor editor;

        //clean up form errors gumbug-3079
        postAdFormBeanWithAction.getFormErrors().clear();

        if (getAuthenticatedUserSession().getUserType().equals(UserLoginStatus.NEW_UNREGISTERED)) {
            postAdFormBeanWithAction.setContactEmail(postAdFormBeanWithAction.getEmailAddress());
        }

        // Get 'user-agent'
        String userAgent = request.getHeader("user-agent");
        String appVersion = postAdFormBeanWithAction.getExtendFields().get("appVersion");
        // Check if the user is a ProUser and the category is 'Cameras, Camcorders & Studio Equipment  > Lenses'
        editor = getPostAdWorkspace().getEditor(editorId);
        Long categoryId = editor.getCategoryId() != null ? editor.getCategoryId() : postAdFormBeanWithAction.getCategoryId();
        if (!getAuthenticatedUserSession().isProUser()
                && AppVersionUtils.isPhoneVerificationVersion(userAgent, appVersion)
                && (categoryId != null
                    && (categoryId == 10985L || categoryId == 93L || categoryId == 8669L || categoryId == 10538L))) {
            //Judge whether mobile phone number verification is required
            AuthenticationStatus authenticationStatus = phoneVerifyService.getAuthenticationStatus(getAuthenticatedUserSession().getUser().getId());
            if (!AuthenticationStatus.VERIFIED.equals(authenticationStatus)) {
                postAdFormBeanWithAction.handleUnknownProperties(IS_PHONE_VERIFICATION_MANDATORY, true);
            }
        }

        // user wants to remove draft
        if (Boolean.TRUE.equals(postAdFormBeanWithAction.getRemoveDraft())) {
            PermanentCookie permanentCookie = cookieResolver.resolve(request, PermanentCookie.class);
            ThreatMetrixCookie threatMetrixCookie = cookieResolver.resolve(request, ThreatMetrixCookie.class);
            editor = getPostAdWorkspace().resetEditor(editorId, remoteIP, permanentCookie, threatMetrixCookie);
        } else {
            editor.setPostAdFormBean(postAdFormBeanWithAction);
            getPostAdWorkspace().updateEditor(editor.getEditorId(), editor);
        }
        metrics.postAdSubmitPageTimer("updateEditor").record(System.currentTimeMillis() - startUpdate, TimeUnit.MILLISECONDS);

        Optional<Category> l1Category = categoryModel.getL1CategoryFor(postAdFormBeanWithAction.getCategoryId());

        PostAdSubmitModel.Builder model = PostAdSubmitModel.builder()
                .withUserLoggedIn(isUserLoggedIn())
                .withL1Category(l1Category);

        long startSteps = System.currentTimeMillis();
        Optional<List<AttributeMetadata>> attributes = Optional.absent();
        if (categoryId != null) {
            attributes = categoryModel.getCategoryAttributes(categoryId);
        }

        log.info("IsAttributeStep:"+postAdFormBeanWithAction.getAttributeStep()+",steps:"+postAdSteps.stream().map(step -> step.getClass().getSimpleName()).collect(Collectors.joining(",")));
        if(postAdFormBeanWithAction.getAttributeStep() != null && postAdFormBeanWithAction.getAttributeStep() && attributes != null && attributes.isPresent()){
            PostAdStep step = postAdSteps.stream().filter(postAdStep -> isCategoryAttributeSelectPostAdStep(postAdStep.getClass()))
                    .findFirst().orElse(new CategoryAttributeSelectPostAdStep(categoryService, attributePresentationService, postAdImageConverter, descriptionHintService));
            //add aiPostFlowType
            if(StringUtils.isNotEmpty(postAdFormBeanWithAction.getAiFlowType())){
                Map<String, String> extendFields = postAdFormBeanWithAction.getExtendFields();
                if (extendFields == null) {
                    extendFields = new HashMap<>();
                    postAdFormBeanWithAction.setExtendFields(extendFields);
                }
                extendFields.put("aiPostFlowType", postAdFormBeanWithAction.getAiFlowType());
            }
            if (!step.execute(model, editor)) {
                return model.build();
            }
        }else{
            List<PostAdStep> steps = postAdSteps.stream().filter(postAdStep -> !isCategoryAttributeSelectPostAdStep(postAdStep.getClass())).collect(Collectors.toList());
            for (PostAdStep step : steps) {
                if (!step.execute(model, editor)) {
                    return model.build();
                }
            }
        }

        metrics.postAdSubmitPageTimer("executeSteps").record(System.currentTimeMillis() - startSteps, TimeUnit.MILLISECONDS);
        return model.build();
    }

    private static boolean isCategoryAttributeSelectPostAdStep(Class<? extends PostAdStep> valueClass){
        return CategoryAttributeSelectPostAdStep.class.getName().equals(valueClass.getName());
    }

    @Override
    protected String resolveMessage(String code, Object... args) {
        return getMessageResolver().getMessage(code, code, args);
    }

    /**
     * Wrapper around form bean & removeDraft action so bean wouldn't be polluted
     */
     public static class PostAdFormBeanWithAction extends PostAdFormBean {
        private Boolean removeDraft;

        private Boolean attributeStep;
        //AIPost flow type
        private String aiFlowType;

        public Boolean getRemoveDraft() {
            return removeDraft;
        }

        public void setRemoveDraft(Boolean removeDraft) {
            this.removeDraft = removeDraft;
        }

        public Boolean getAttributeStep() {
            return attributeStep;
        }

        public void setAttributeStep(Boolean attributeStep) {
            this.attributeStep = attributeStep;
        }

        public String getAiFlowType() {
            return aiFlowType;
        }

        public void setAiFlowType(String aiFlowType) {
            this.aiFlowType = aiFlowType;
        }
    }
}
