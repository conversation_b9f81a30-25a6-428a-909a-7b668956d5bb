package com.gumtree.web.seller.page.postad.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.postad.model.OrderErrorModel;
import com.gumtree.zeno.core.domain.PageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

import static com.gumtree.web.seller.page.postad.controller.OrderErrorController.PAGE_PATH;

@Controller
@GumtreePage(PageType.OrderPostAdError)
@RequestMapping(PAGE_PATH)
@GoogleAnalytics
public class OrderErrorController extends BaseSellerController {

    public static final String PAGE_PATH = "/postad/order-error";

    @Autowired
    public OrderErrorController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                UrlScheme urlScheme, UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
    }

    @RequestMapping(method = RequestMethod.GET)
    public final ModelAndView viewErrorPage(HttpServletRequest request) {
        return OrderErrorModel.builder().build(getCoreModelBuilder(request));
    }
}
