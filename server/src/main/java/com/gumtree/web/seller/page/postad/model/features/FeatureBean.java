package com.gumtree.web.seller.page.postad.model.features;

import java.io.Serializable;

/**
 * Represents a feature selection on the post/edit ad form.
 */
public final class FeatureBean implements Serializable {

    private Boolean selected;

    private String productName;

    /**
     * Default constructor
     */
    public FeatureBean() {

    }

    public Boolean getSelected() {
        return selected;
    }

    public void setSelected(<PERSON><PERSON><PERSON> selected) {
        this.selected = selected;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
}
