package com.gumtree.web.seller.page.postad.model;

import com.gumtree.web.seller.page.common.SelectableValue;

/**
 * Represents a bump up option of the bump up form
 */
public final class BumpUpOption implements SelectableValue {

    private String description;

    private String value;

    /**
     * Constructor.
     *
     * @param description the description of this option
     * @param value       the actual value of this option
     */
    public BumpUpOption(String description, String value) {
        this.description = description;
        this.value = value;
    }

    @Override
    public String getDisplayValue() {
        return description;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof BumpUpOption)) {
            return false;
        }

        BumpUpOption that = (BumpUpOption) o;

        if (description != null ? !description.equals(that.description) : that.description != null) {
            return false;
        }

        if (value != null ? !value.equals(that.value) : that.value != null) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = description != null ? description.hashCode() : 0;
        result = 31 * result + (value != null ? value.hashCode() : 0);
        return result;
    }
}
