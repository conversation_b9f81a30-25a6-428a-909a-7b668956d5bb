package com.gumtree.web.seller.page.ajax.adcontent;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.service.ai.AISuggestService;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class AdContentSuggestController extends BaseSellerController {

    private final CustomMetricRegistry metrics;

    AISuggestService aiSuggestService;

    @Autowired
    public AdContentSuggestController(CookieResolver cookieResolver, CategoryModel categoryModel, ApiCallExecutor apiCallExecutor,
                                      ErrorMessageResolver messageResolver, UrlScheme urlScheme, UserSessionService userSessionService,
                                      CustomMetricRegistry customMetricRegistry, AISuggestService aiSuggestService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.metrics = customMetricRegistry;
        this.aiSuggestService = aiSuggestService;
    }


    /**
     * after user type in the original words, give some title suggest to users to select
     * @param input title words
     * @return recommend titles
     */
    @RequestMapping(value = "/api/title/AIsuggest", method = RequestMethod.GET)
    @ResponseBody
    public List<String> titleAISuggest(@RequestParam(value = "input") final String input) {
        return metrics.AIResponseTimer("titleSuggest").record(()->aiSuggestService.getSuggestedTitles(input));
    }

    /**
     * after user type in the original words, give some title and description suggest to users to select
     * this method is also used for update the title and description suggests to users
     * @param categoryName category name
     * @param title title
     * @param desc  description
     * @param images imageUrl
     * @param attributes attributes
     * @return title and description suggest
     */
    @RequestMapping(value = "/api/titleAndDesc/AIsuggest", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, String> titleAndDescAISuggest(@RequestParam(value = "categoryName") final String categoryName, @RequestParam(value = "title") final String title,
                                                     @RequestParam(value = "desc") final String desc, @RequestParam(value = "images") final String images,
                                                     @RequestParam(value = "attributes") final JSONObject attributes) {
        if (metrics == null || aiSuggestService == null) {
            log.warn("metrics or aiSuggestService is null");
            return Collections.emptyMap();
        }

        try {
            Timer timer = metrics.AIResponseTimer("titleDescSuggest");
            if (timer == null) {
                return aiSuggestService.getSuggestedTitleAndDesc(categoryName, attributes, title, desc, images);
            }

            return timer.record(() -> aiSuggestService.getSuggestedTitleAndDesc(categoryName, attributes, title, desc, images));
        } catch (Exception e) {
            log.error("Error in titleAndDescAISuggest", e);
            return Collections.emptyMap();
        }
    }

}
