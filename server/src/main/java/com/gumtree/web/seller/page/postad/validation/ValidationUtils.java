package com.gumtree.web.seller.page.postad.validation;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class ValidationUtils {
    public static final int DESCRIPTION_MIN_CHAR_LENGTH = 15;

    public static boolean matches(String text, ValidationRegexp regexp) {
        if (StringUtils.isBlank(text)){
            return false;
        }
        Pattern pattern = Pattern.compile(regexp.getRegexp());
        Matcher matcher = pattern.matcher(text);
        return matcher.find();
    }

    enum ValidationRegexp {
        PHONE_NUMBER_REGEXP("(?:[oO]|\\d)[- .\\*()#]*(?:[\\n\\dIiOo][- .\\*()#]*){9}\\d");

        final String regexp;

        ValidationRegexp(String regexp) {
            this.regexp = regexp;
        }

        public String getRegexp() {
            return regexp;
        }
    }
}
