package com.gumtree.web.seller.page.postad.model.products;

import com.gumtree.web.seller.page.common.SelectableValue;

import java.math.BigDecimal;

/**
 * Represents a price for a product
 */
public interface ProductPrice extends SelectableValue {

    /**
     * @return the product type associated with this price
     */
    ProductType getProductType();

    /**
     * @return the associated unique product name
     */
    String getProductName();

    /**
     * @return the display price
     */
    BigDecimal getPrice();

    /**
     * @return if is free - shortcut instead of having to check price being 0
     */
    boolean isFree();

    boolean isIncludedInPackage();

    void setDiscountedBy(Integer percent);
}
