package com.gumtree.web.seller.page.postad.controller.steps;

import com.google.common.base.Function;
import com.google.common.base.Optional;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.gumtree.api.Image;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.Category;
import com.gumtree.mobile.web.category.BrowseCategory;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.seller.page.postad.converter.PostAdImageConverter;
import com.gumtree.web.seller.page.postad.model.*;
import com.gumtree.web.seller.page.postad.service.PostAdFormDescriptionHintService;
import com.gumtree.web.seller.service.presentation.config.AttributePresentationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.SELLER_TYPE;
import static com.gumtree.web.seller.page.postad.service.PostAdFormDescriptionHintService.HintSection.IMAGES;

@Slf4j
@Component
public class CategoryAttributeSelectPostAdStep implements PostAdStep {
    Logger LOGGER = LoggerFactory.getLogger(CategoryAttributeSelectPostAdStep.class);

    public static final Integer ORDER = 2;

    CategoryService categoryService;

    AttributePresentationService attributePresentationService;

    PostAdImageConverter postAdImageConverter;

    PostAdFormDescriptionHintService descriptionHintService;

    @Autowired
    public CategoryAttributeSelectPostAdStep(CategoryService categoryService, AttributePresentationService attributePresentationService
            , PostAdImageConverter postAdImageConverter, PostAdFormDescriptionHintService descriptionHintService) {
        this.categoryService = categoryService;
        this.attributePresentationService = attributePresentationService;
        this.postAdImageConverter = postAdImageConverter;
        this.descriptionHintService = descriptionHintService;
    }

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    public boolean execute(PostAdSubmitModel.Builder model, AdvertEditor editor) {
        model.withForm(editor.getPostAdFormBean());
        List<PostAdFormPanel> panels = getPanels(editor);
        log.info("panels:" + panels.stream().map(PostAdFormPanel::name).collect(Collectors.joining(",")));

        panels = filterPanels(panels);
        model.withCategoryCrumb(getCategoryCrumb(editor));
        model.withPanel(PostAdFormPanel.CATEGORY);
        Map<String, String> attributes = editor.getPostAdFormBean().getAttributes();
        model.withPanelAttributes(
                attributePresentationService.loadAttributeGroups(editor.getCategoryId(), attributes));

        model.withImages(convertImages(editor.getImages()));
        model.withImagesHint(
                descriptionHintService.getHint(IMAGES, editor.getCategoryId(), editor.getLocationId()));

        Optional<AttributeMetadata> sellerTypeAttribute =
                categoryService.getCategoryModel().findAttributesByNameForGivenCategory(SELLER_TYPE.getName(), editor.getCategoryId());

        if (sellerTypeAttribute == null || !sellerTypeAttribute.isPresent() || !sellerTypeAttribute.get().isPriceSensitive() || model.isSellerTypeSelected()) {
            if (model.hasBrands()) {
                int index = panels.indexOf(PostAdFormPanel.DESCRIPTION);
                if (index < 0) {
                    index = panels.size();
                }
                panels.add(index, PostAdFormPanel.BRANDS);
            }
        }

        model.addPanels(panels);
        return true;
    }

    public List<PostAdFormPanel> filterPanels(List<PostAdFormPanel> panels) {
        if (CollectionUtils.isEmpty(panels)) {
            return new ArrayList<>();
        }
        return panels.stream().filter(panel -> panel != PostAdFormPanel.PRICE && panel != PostAdFormPanel.SELLER_TYPE).collect(Collectors.toList());
    }

    public List<BrowseCategory> getCategoryCrumb(AdvertEditor editor) {
        try {
            Function<Category, BrowseCategory> function = toBrowseCategory(editor.getCategoryId());
            return Lists.newLinkedList(Iterables.transform(getCategoryCrumb(editor.getCategoryId()), function));
        } catch (Exception e) {
            LOGGER.error("Failed to get category crumb, advert category {}", editor.getCategoryId());
            throw e;
        }
    }

    private List<Category> getCategoryCrumb(long categoryId) {
        Optional<Category> category = categoryService.getById(categoryId);
        if (category != null && category.isPresent()) {
            Map<Integer, Category> categoryMap = categoryService.getLevelHierarchy(category.get());
            return categoryMap.values().stream().skip(1)
                    .filter(c -> !c.isHidden()).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }
    }

    private Function<Category, BrowseCategory> toBrowseCategory(final Long catId) {
        return new Function<Category, BrowseCategory>() {
            @Nullable
            @Override
            public BrowseCategory apply(@Nullable Category input) {
                return new BrowseCategory(input, Lists.<BrowseCategory>newLinkedList(), input.getId().equals(catId));
            }
        };
    }

    List<PostAdFormPanel> getPanels(AdvertEditor editor) {
        List<PostAdFormPanel> orderedPanels = new ArrayList<>();
        if (editor.isCreateMode()) {
            orderedPanels.add(PostAdFormPanel.IMAGES);
        }

        PostAdFormBean postAdFormBean = editor.getPostAdFormBean();
        Map<String, String> formAttributes = postAdFormBean.getAttributes();
        CategorySpecificPostAdFormPanels categorySpecificPostAdFormPanels =
                attributePresentationService.loadPrioritisedCategorySpecificFormPanels(editor.getCategoryId(), formAttributes);

        if (categorySpecificPostAdFormPanels != null) {
            List<PostAdFormPanel> lowPriorityPanels = categorySpecificPostAdFormPanels.getLowPriorityPanels();
            if (CollectionUtils.isNotEmpty(lowPriorityPanels)) {
                log.info("lowPriorityPanels:" + lowPriorityPanels.stream().map(PostAdFormPanel::name).collect(Collectors.joining(",")));
                orderedPanels.addAll(lowPriorityPanels);

            }
            List<PostAdFormPanel> highPriorityPanels = categorySpecificPostAdFormPanels.getHighPriorityPanels();
            if (CollectionUtils.isNotEmpty(highPriorityPanels)) {
                log.info("highPriorityPanels:" + highPriorityPanels.stream().map(PostAdFormPanel::name).collect(Collectors.joining(",")));
                orderedPanels.addAll(highPriorityPanels);
            }
        }

        //if return pre-confirm panel, syi enter into the middle page
        Map<String, String> extendFields = editor.getPostAdFormBean().getExtendFields();
        if (!StringUtils.equals(extendFields.get("aiPostFlowType"), "B") && formAttributes.size() == 1 && formAttributes.containsKey("price")) {
            return orderedPanels;
        } else {
            orderedPanels.add(PostAdFormPanel.PRE_CONFIRM);
            return orderedPanels;
        }
    }

    public List<PostAdImage> convertImages(List<Image> images) {
        if (images == null) {
            return new ArrayList<>();
        }
        return images.stream().map(postAdImageConverter::convertImageToPostAdImage).collect(Collectors.toList());
    }

}
