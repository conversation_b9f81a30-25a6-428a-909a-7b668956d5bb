package com.gumtree.legacy.impl;

import com.google.common.collect.ImmutableMap;
import com.gumtree.common.util.IdMapping;
import com.gumtree.common.util.delimited.DelimitedTemplate;
import com.gumtree.legacy.LegacySite;
import com.gumtree.legacy.LegacySiteMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * Map of legacy sites.
 */
@Component
public class LegacySiteMapImpl implements LegacySiteMap, InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(LegacySiteMapImpl.class);

    private static Map<Integer, Integer> oldCountyIdMap;

    private static Map<Integer, Integer> newIdToOldCountyMap;

    static {
        oldCountyIdMap = new HashMap<Integer, Integer>();
        oldCountyIdMap.put(11000002, 10000348); // bristol
        oldCountyIdMap.put(11000015, 10000384); // essex
        oldCountyIdMap.put(11000017, 10000344); // london
        oldCountyIdMap.put(11000018, 10000352); // manchester
        oldCountyIdMap.put(11000023, 10000382); // kent
        oldCountyIdMap.put(11000024, 10000388); // lancashire
        oldCountyIdMap.put(11000051, 10000354); // cardiff
        oldCountyIdMap.put(11000066, 10000372); // swansea
        oldCountyIdMap.put(11000070, 10000376); // aberdeen
        oldCountyIdMap.put(11000076, 10000379); // dundee
        oldCountyIdMap.put(11000081, 10000345); // edinburgh
        oldCountyIdMap.put(11000084, 10000349); // glasgow

        // Generate reverse mapping
        newIdToOldCountyMap = new HashMap<Integer, Integer>();
        for (Map.Entry<Integer, Integer> entry : oldCountyIdMap.entrySet()) {
            newIdToOldCountyMap.put(entry.getValue(), entry.getKey());
        }
    }

    /**
     * Simple utility class for capturing each line read from our properties file.
     * Captures the legacySite and whether or not the entry is a 1->1 site mapping, or not
     */
    private final class SiteEntry {
        /**
         * Get the LegacySite for this entry
         *
         * @return the LegacySite
         */
        public LegacySite getSite() {
            return site;
        }

        /**
         * Return true if this entry is a site
         *
         * @return whether this entry is a site, or not
         */
        public boolean isSite() {
            return isSite;
        }

        private LegacySite site;
        private boolean isSite;

        /**
         * Constructor for simple SiteEntry object
         *
         * @param site   the LegacySite
         * @param isSite whether this entry is a site or not
         */
        public SiteEntry(LegacySite site, boolean isSite) {
            this.site = site;
            this.isSite = isSite;
        }
    }


    // This maps ALL location seo_names to LegacySite objects
    private ImmutableMap<String, LegacySite> siteMap;

    private ImmutableMap<Integer, String> locationIdToName;

    private ImmutableMap<Integer, Integer> oldCategoryToLocationIdMap;

    private ImmutableMap<Integer, Integer> siteIdToLocationIdMap;

    private ImmutableMap<Integer, Integer> locationIdToOldCategoryMap;

    private Map<Long, Integer> postingCategoryIdToLocationIdMap = new HashMap<Long, Integer>();

    @Autowired
    @Qualifier("siteProperties")
    private Resource sitePropertiesFile;

    @Autowired
    @Qualifier("postingCategoryMappings")
    private Resource postingCategoryMappingsFile;

    /**
     * @param locationName name
     * @return This function will return a legacy site object, the object contains two strings holding
     *         the following information, siteId and siteName.
     */
    public final LegacySite getLegacySite(String locationName) {
        LegacySite legacySite = null;
        try {
            legacySite = siteMap.get(locationName);
        } catch (IllegalAccessError e) {
            LOGGER.info("Failed to get a valid LegacySite object from siteMap: " + e);
        }
        return legacySite;
    }

    /**
     * Get the legacy site object for the specified location id
     *
     * @param locationId current location
     * @return The legacy site object pertaining to this location
     *         <p/>
     *         TODO: The locationIdToName is a waste, refactor to just have getLegacySite(int),
     *         TODO: make siteMap Integer->LegacySite and get rid of the name map
     */
    public final LegacySite getLegacySite(int locationId) {
        LegacySite legacySite = null;
        try {
            String name = locationIdToName.get(locationId);
            if (name != null) {
                legacySite = siteMap.get(name);
            }
        } catch (IllegalAccessError e) {
            LOGGER.info("Failed to get a valid LegacySite object from siteMap: " + e);
        }
        return legacySite;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final boolean hasSite(int locationId) {
        return locationIdToName.containsKey(locationId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final int transformOldCountyId(int oldCountyId) {
        Integer newId = oldCountyIdMap.get(oldCountyId);
        return newId != null ? newId : oldCountyId;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final int transformToOldCountyId(int newLocationId) {
        Integer oldId = newIdToOldCountyMap.get(newLocationId);
        return oldId != null ? oldId : newLocationId;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final Integer getLocationIdForPostingCategoryId(Long postingCategoryId) {
        if (postingCategoryId != null) {
            return postingCategoryIdToLocationIdMap.get(postingCategoryId);
        }
        return null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final Integer getLocationIdForPostingCategoryId(Integer postingCategoryId) {
        if (postingCategoryId != null) {
            return postingCategoryIdToLocationIdMap.get(postingCategoryId.longValue());
        }
        return null;
    }

    /**
     * TODO: Needs unit testing
     *
     * @throws Exception on parse failure
     */
    @Override
    public final void afterPropertiesSet() throws Exception {
        Properties siteProps = new Properties();
        InputStream siteStream = sitePropertiesFile.getInputStream();
        siteProps.load(siteStream);

        ImmutableMap.Builder<String, LegacySite> siteBuilder = new ImmutableMap.Builder<String, LegacySite>();
        ImmutableMap.Builder<Integer, Integer> oldIdToLocationBuilder = new ImmutableMap.Builder<Integer, Integer>();
        ImmutableMap.Builder<Integer, Integer> locationToOldIdBuilder = new ImmutableMap.Builder<Integer, Integer>();
        ImmutableMap.Builder<Integer, String> idToNameBuilder = new ImmutableMap.Builder<Integer, String>();
        ImmutableMap.Builder<Integer, Integer> siteIdToLocationIdMapBuilder
                = new ImmutableMap.Builder<Integer, Integer>();
        for (Map.Entry propEntry : siteProps.entrySet()) {
            SiteEntry siteEntry = parseProperty(propEntry);
            if (siteEntry.getSite() != null) {
                siteBuilder.put((String) propEntry.getKey(), siteEntry.getSite());

                if (siteEntry.isSite()) {
                    try {
                        idToNameBuilder.put(siteEntry.getSite().getLocationId(), propEntry.getKey().toString());
                        oldIdToLocationBuilder.put(siteEntry.getSite().getOldCategoryId(),
                                siteEntry.getSite().getLocationId());
                        locationToOldIdBuilder.put(siteEntry.getSite().getLocationId(),
                                siteEntry.getSite().getOldCategoryId());
                        siteIdToLocationIdMapBuilder.put(siteEntry.getSite().getId(),
                                siteEntry.getSite().getLocationId());
                    } catch (Exception e) {
                        LOGGER.warn("Couldn't get location with name " + propEntry.getKey());
                    }
                }
            }
        }

        siteMap = siteBuilder.build();
        locationIdToName = idToNameBuilder.build();
        oldCategoryToLocationIdMap = oldIdToLocationBuilder.build();
        locationIdToOldCategoryMap = locationToOldIdBuilder.build();
        siteIdToLocationIdMap = siteIdToLocationIdMapBuilder.build();

        // Posting category mapping
        loadPostingCategoryMappings();
    }

    private SiteEntry parseProperty(Map.Entry property) throws Exception {
        String entry = (String) property.getValue();
        String[] properties = entry.split(",", -1);

        if (properties.length > 3) {
            Integer siteId = Integer.parseInt(properties[1]);
            Boolean isSite = Boolean.parseBoolean(properties[2]);
            Integer oldCategoryId = null;
            Integer locationId = null;
            if (properties[3].length() > 0) {
                oldCategoryId = Integer.parseInt(properties[3]);
                if (properties[4] != null && properties[4].length() > 0) {
                    locationId = Integer.parseInt(properties[4]);
                }
            }
            if (siteId != null) {
                return new SiteEntry(new LegacySite(properties[0], siteId, oldCategoryId, locationId), isSite);
            }
        }

        return null;
    }

    /**
     * Returns true if the given category id has a respective location id
     *
     * @param oldCategoryId the category id to check
     * @return whether category id has a location or not
     */
    public final boolean oldCategoryHasLocation(int oldCategoryId) {
        return oldCategoryToLocationIdMap.containsKey(oldCategoryId);
    }

    /**
     * Returns true if the given location id has a respective old
     * category id
     *
     * @param locationId the location id to check
     * @return whether the location id has an old category or not
     */
    public final boolean locationHasOldCategory(int locationId) {
        return locationIdToOldCategoryMap.containsKey(locationId);
    }

    /**
     * Get the location id for the given old category id, if it has one
     *
     * @param oldCategoryId The old category id to check
     * @return the location id for the given old category id
     */
    public final int getLocationId(int oldCategoryId) {
        if (oldCategoryToLocationIdMap.containsKey(oldCategoryId)) {
           return oldCategoryToLocationIdMap.get(oldCategoryId);
        }
        return 0;
    }

    @Override
    public final Integer getLocationIdForSiteId(int siteId) {
        return siteIdToLocationIdMap.get(siteId);
    }

    /**
     * Convert all keys in the given map from site ids to location ids
     *
     * @param oldCategoryIdMap the map to transform
     * @return a new map, with transformed keys
     */
    public final Map<Integer, Integer> convertToLocationIds(Map<Integer, Integer> oldCategoryIdMap) {
        Map<Integer, Integer> convertedMap = new HashMap<Integer, Integer>();

        if (oldCategoryIdMap != null) {
            for (Map.Entry<Integer, Integer> entry : oldCategoryIdMap.entrySet()) {
                if (oldCategoryHasLocation(entry.getKey())) {
                    convertedMap.put(getLocationId(entry.getKey()), entry.getValue());
                }
            }
        }

        return convertedMap;
    }

    /**
     * Load mappings between legacy posting categories and locations in the new world.
     *
     * @throws java.io.IOException if can't parse mappings file successfully
     */
    private void loadPostingCategoryMappings() throws IOException {
        DelimitedTemplate delimitedTemplate = new DelimitedTemplate();

        List<IdMapping<Long, Integer>> mappings = delimitedTemplate.parse(
                new InputStreamReader(postingCategoryMappingsFile.getInputStream()),
                ",",
                line -> new IdMapping<>(Long.parseLong(line[0]), Integer.parseInt(line[1])));

        for (IdMapping<Long, Integer> mapping : mappings) {
            postingCategoryIdToLocationIdMap.put(mapping.getSourceId(), mapping.getTargetId());
        }
    }

    public final void setSitePropertiesFile(Resource sitePropertiesFile) {
        this.sitePropertiesFile = sitePropertiesFile;
    }

    public final void setPostingCategoryMappingsFile(Resource postingCategoryMappingsFile) {
        this.postingCategoryMappingsFile = postingCategoryMappingsFile;
    }
}