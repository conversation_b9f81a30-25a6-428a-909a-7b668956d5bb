package com.gumtree.legacy;

import java.util.Map;

/**
 * User: rajisingh
 * Date: 27/04/11
 * Time: 14:32
 */
public interface LegacySiteMap {

    /**
     * @param locationName the name of the location
     * @return This function will return a legacy site object, the object contains two strings holding
     *         the following information, siteId and siteName.
     */
    LegacySite getLegacySite(String locationName);

    /**
     * Get the legacy site object for the specified location id
     *
     * @param locationId current location
     * @return The legacy site object pertaining to this location
     */
    LegacySite getLegacySite(int locationId);

    /**
     * Tells whether the specified location id has an associated legacy site, or not
     *
     * @param locationId the location id to check
     * @return true if locationId has an associated legacy site
     */
    boolean hasSite(int locationId);

    /**
     * Returns true if the given category id has a respective location id
     *
     * @param oldCategoryId the category id to check
     * @return whether category id has a location or not
     */
    boolean oldCategoryHasLocation(int oldCategoryId);

    /**
     * Returns true if the given location id has a respective old
     * category id
     *
     * @param locationId the location id to check
     * @return whether the location id has an old category or not
     */
    boolean locationHasOldCategory(int locationId);

    /**
     * Get the location id for the given old category id, if it has one
     *
     * @param oldCategoryId The old category id to check
     * @return the location id for the given old category id
     */
    int getLocationId(int oldCategoryId);

    /**
     * Get the location id for the given site id
     *
     * @param siteId the site id to look up
     * @return the location id for the given site id
     */
    Integer getLocationIdForSiteId(int siteId);

    /**
     * Get the location id for the given old county id.
     *
     * @param oldCountyId the old county id
     * @return the location id for the given old county id.
     */
    int transformOldCountyId(int oldCountyId);

    /**
     * Get the old county id for the given new locatoin id.
     *
     * @param newLocationId the new location id
     * @return the old county id for the given location id
     */
    int transformToOldCountyId(int newLocationId);

    /**
     * @param postingCategoryId the legacy posting category id
     * @return the location id it maps to in the new system
     */
    Integer getLocationIdForPostingCategoryId(Long postingCategoryId);

    /**
     * @param postingCategoryId the legacy posting category id
     * @return the location id it maps to in the new system
     */
    Integer getLocationIdForPostingCategoryId(Integer postingCategoryId);

    /**
     * Convert all keys in the given map from site ids to location ids
     *
     * @param oldCategoryIdMap the map to transform
     * @return a new map, with transformed keys
     */
    Map<Integer, Integer> convertToLocationIds(Map<Integer, Integer> oldCategoryIdMap);
}