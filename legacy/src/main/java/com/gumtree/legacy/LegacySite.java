package com.gumtree.legacy;

/**
 * The LegacySite class is an object that is used to hold the babygumId and babygumName.  A map
 * of these values is populated by a page controller and then accessed by several reporting tools
 * when needed.
 */
public class LegacySite {
    private String name;
    private Integer id;
    private Integer oldCategoryId;
    private Integer locationId;

    /**
     *
     * @param name of the site (babygum)
     * @param id of the site (babygum)
     * @param oldCategoryId the old posting_cat root id for this site
     * @param locationId the location id
     */
    public LegacySite(String name, Integer id, Integer oldCategoryId, Integer locationId) {
        this.name = name;
        this.id = id;
        this.oldCategoryId = oldCategoryId;
        this.locationId = locationId;
    }

    public final String getName() {
        return name;
    }

    public final Integer getId() {
        return id;
    }

    public final Integer getOldCategoryId() {
        return oldCategoryId;
    }

    public final Integer getLocationId() {
        return locationId;
    }
}