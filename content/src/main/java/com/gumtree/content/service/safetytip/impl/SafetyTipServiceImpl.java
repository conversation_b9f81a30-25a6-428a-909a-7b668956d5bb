package com.gumtree.content.service.safetytip.impl;

import com.google.common.base.Optional;
import com.gumtree.content.domain.SafetyTip;
import com.gumtree.content.repository.SafetyTipRepository;
import com.gumtree.content.service.safetytip.SafetyTipService;
import com.gumtree.api.category.domain.Category;
import com.gumtree.service.category.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * SafetyTipService implementation
 */
@Component
public class SafetyTipServiceImpl implements SafetyTipService {

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private SafetyTipRepository safetyTipRepository;

    @Override
    public List<SafetyTip> getSafetyTipsByCategoryId(Long categoryId) {

        List<SafetyTip> safetyTips;
        safetyTips = safetyTipRepository.findByCategoryId(categoryId);
        Optional<Category> category = categoryService.getById(categoryId);
        while ((safetyTips == null || safetyTips.isEmpty()) && category.isPresent()) {
            category = categoryService.getById(category.get().getParentId());
            safetyTips = safetyTipRepository.findByCategoryId(category.get().getId());
        }
        return safetyTips;
    }
}
