package com.gumtree.content.repository.impl;

import com.google.common.collect.Multimap;
import com.gumtree.common.util.delimited.DelimitedMapLineMapper;
import com.gumtree.common.util.delimited.DelimitedMultimapParser;
import com.gumtree.content.domain.SafetyTip;
import org.junit.Before;
import org.junit.Test;
import org.springframework.core.io.Resource;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Reader;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 */
public class CSVSafetyTipRepositoryTest {

    private Resource safetyTipData;
    private Multimap<Long, SafetyTip> safetyTipsMap;

    @Before
    public void init() {
        safetyTipData = mock(Resource.class);
    }

    @Test
    public void testLoadSafetyTipData() throws IOException {

        byte[] buffer = {1, 2, 3};
        InputStream inputStream = new ByteArrayInputStream(buffer);
        when(safetyTipData.getInputStream()).thenReturn(inputStream);

        DelimitedMultimapParser delimitedMultimapParser = mock(DelimitedMultimapParser.class);
        DelimitedMapLineMapper<Long, SafetyTip> delimitedMapLineMapper = mock(DelimitedMapLineMapper.class);
        CSVSafetyTipRepository csvSafetyTipRepository = new CSVSafetyTipRepository(delimitedMultimapParser,
                delimitedMapLineMapper,
                safetyTipData);

        verify(delimitedMultimapParser).parse(any(Reader.class), eq("\\|"), eq(delimitedMapLineMapper));

    }
}
