<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       						http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
       						http://www.springframework.org/schema/context
       						http://www.springframework.org/schema/context/spring-context.xsd">

    <context:property-placeholder
            location="classpath:/com/gumtree/test-application.properties"
            ignore-resource-not-found="true"
            system-properties-mode="OVERRIDE"/>

    <bean id="siteProperties" class="org.springframework.core.io.ClassPathResource">
        <constructor-arg value="siteId.properties"/>
    </bean>

    <bean id="postingCategoryMappings" class="org.springframework.core.io.ClassPathResource">
        <constructor-arg value="postingCategoryMappings.csv"/>
    </bean>

    <bean id="jobConfiguration" class="org.springframework.core.io.ClassPathResource">
        <constructor-arg value="JobsLandingPageCateforyStructure.properties"/>
    </bean>


</beans>