package com.gumtree.common.model.attribute;



import org.mockito.Mockito;

import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.AttributeValue;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

public class AttributeMockingUtils {

    public static Attribute mock(String attributeType, Object value) {
        Attribute mockedAttribute = Mockito.mock(Attribute.class);
        AttributeValue mockedAttributeValue = Mockito.mock(AttributeValue.class);

        when(mockedAttribute.getType()).thenReturn(attributeType);
        when(mockedAttribute.getValue()).thenReturn(mockedAttributeValue);
        when(mockedAttributeValue.as(any(Class.class))).thenReturn(value);

        return mockedAttribute;
    }

}
