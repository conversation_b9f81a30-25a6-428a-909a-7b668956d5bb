package com.gumtree.common.model.category.impl.homepage.mapping;

import org.codehaus.jackson.map.ObjectMapper;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 */
public class LocationHomepageCategoryTest {

    private ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testDeserialise() throws Exception {
        String json = "{\"abundance_visible\":true,\"name\":\"pets\",\"sort_group_index\":2}";
        LocationHomepageCategory entity = objectMapper.readValue(json, LocationHomepageCategory.class);
        assertThat(entity.getName(), equalTo("pets"));
        assertThat(entity.isAbundanceVisible(), equalTo(true));
        assertThat(entity.getSortGroupIndex(), equalTo(2));
    }

    @Test
    public void testDeserialiseWithMinimumFields() throws Exception {
        String json = "{\"name\":\"pets\"}";
        LocationHomepageCategory entity = objectMapper.readValue(json, LocationHomepageCategory.class);
        assertThat(entity.getName(), equalTo("pets"));
        assertThat(entity.isAbundanceVisible(), equalTo(null));
        assertThat(entity.getSortGroupIndex(), equalTo(null));
    }
}
