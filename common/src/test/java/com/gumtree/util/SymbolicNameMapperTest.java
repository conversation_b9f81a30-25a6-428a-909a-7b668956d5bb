package com.gumtree.util;

import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

/**
 */
public class SymbolicNameMapperTest {

    @Test
    public void testCategoryIdToSymbolicNameMappings() {

        assertThat(SymbolicNameMapper.getSymbolicNameForCategory(2551L), equalTo("cat_cars_L1"));
        assertThat(SymbolicNameMapper.getSymbolicNameForCategory(2549L), equalTo("cat_for-sale_L1"));
        assertThat(SymbolicNameMapper.getSymbolicNameForCategory(2554L), equalTo("cat_services_L1"));
        assertThat(SymbolicNameMapper.getSymbolicNameForCategory(2553L), equalTo("cat_jobs_L1"));
        assertThat(SymbolicNameMapper.getSymbolicNameForCategory(10201L), equalTo("cat_housing_L1"));
        assertThat(SymbolicNameMapper.getSymbolicNameForCategory(2550L), equalTo("cat_community_L1"));
        assertThat(SymbolicNameMapper.getSymbolicNameForCategory(2526L), equalTo("cat_pets_L1"));
        assertThat(SymbolicNameMapper.getSymbolicNameForCategory(1000003L), equalTo("cat_competitions_L1"));
    }

    @Test
    public void testSymbolicNameToCategoryIdMappings() {

        assertThat(SymbolicNameMapper.getCategoryId("cat_cars_L1"),equalTo(2551L));
        assertThat(SymbolicNameMapper.getCategoryId("cat_for-sale_L1"),equalTo(2549L));
        assertThat(SymbolicNameMapper.getCategoryId("cat_services_L1"),equalTo(2554L));
        assertThat(SymbolicNameMapper.getCategoryId("cat_jobs_L1"),equalTo(2553L));
        assertThat(SymbolicNameMapper.getCategoryId("cat_housing_L1"),equalTo(10201L));
        assertThat(SymbolicNameMapper.getCategoryId("cat_community_L1"),equalTo(2550L));
        assertThat(SymbolicNameMapper.getCategoryId("cat_pets_L1"),equalTo(2526L));
        assertThat(SymbolicNameMapper.getCategoryId("cat_competitions_L1"),equalTo(1000003L));
    }
}
