package com.gumtree.util;

import com.gumtree.domain.location.Location;
import com.gumtree.common.model.location.LocationModel;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public final class LocationTestUtils {

    private LocationTestUtils() {

    }

    public static Location createLocation(
            Integer id,
            String name,
            String displayName) {
        return createLocation(id, name, displayName, false);
    }

    public static Location createLocation(
            Integer id,
            String name,
            String displayName,
            boolean isLanding) {
        Location location = mock(Location.class);
        when(location.getId()).thenReturn(id);
        when(location.getName()).thenReturn(name);
        when(location.getDisplayName()).thenReturn(displayName);
        when(location.isLanding()).thenReturn(isLanding);
        return location;
    }

    public static LocationModel createNoAssociationsLocationModel() {
        LocationModel locationModel = mock(LocationModel.class);
        when(locationModel.getZoomOut(any(Location.class))).thenReturn(new ArrayList<>());
        when(locationModel.getZoomIn(any(Location.class))).thenReturn(new ArrayList<>());
        when(locationModel.getNearby(any(Location.class))).thenReturn(new ArrayList<>());
        return locationModel;
    }

    public static void addAssociations(
            LocationModel model,
            Location location,
            List<Location> zoomIns,
            List<Location> zoomOuts,
            List<Location> nearbys) {
        addZoomIns(model, location, zoomIns);
        addZoomOuts(model, location, zoomOuts);
        addNearbys(model, location, nearbys);
    }

    public static void addZoomOuts(LocationModel model, Location location, List<Location> zoomOuts) {
        when(model.getZoomOut(location)).thenReturn(zoomOuts);
    }

    public static void addZoomIns(LocationModel model, Location location, List<Location> zoomIns) {
        when(model.getZoomIn(location)).thenReturn(zoomIns);
    }

    public static void addNearbys(LocationModel model, Location location, List<Location> nearbys) {
        when(model.getNearby(location)).thenReturn(nearbys);
    }
}
