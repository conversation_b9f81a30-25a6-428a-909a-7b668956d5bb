package com.gumtree.domain;

import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class ZeroablePageNumberTest {

    @Test
    public void shouldCreateNewInstanceOk() {
        assertThat(ZeroablePageNumber.of(0).getValue()).isEqualTo(0);
        assertThat(ZeroablePageNumber.of(1).getValue()).isEqualTo(1);
        assertThat(ZeroablePageNumber.of(10).getValue()).isEqualTo(10);
        assertThat(ZeroablePageNumber.of(9999).getValue()).isEqualTo(9999);
        assertThat(ZeroablePageNumber.of(9999)).isEqualTo(new ZeroablePageNumber(9999));
        assertThat(ZeroablePageNumber.of(NonZeroPageNumber.of(1))).isEqualTo(ZeroablePageNumber.of(0));
        assertThat(ZeroablePageNumber.of(NonZeroPageNumber.of(2))).isEqualTo(ZeroablePageNumber.of(1));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldFailIfInputIsLessThanZero() {
        new ZeroablePageNumber(-1);
    }
}