package com.gumtree.service.attribute;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * User: oliver
 * Date: 09/01/12
 * Time: 11:31
 * To change this template use File | Settings | File Templates.
 */

public final class Attribute {

    private String name;

    private String type;

    private String read;

    private String write;

    private String required;

    private List<AttributeValue> values;

    private String read_from_search;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRead() {
        return read;
    }

    public void setRead(String read) {
        this.read = read;
    }

    public String getWrite() {
        return write;
    }

    public void setWrite(String write) {
        this.write = write;
    }

    public String getRequired() {
        return required;
    }

    public void setRequired(String required) {
        this.required = required;
    }

    public String getRead_from_search() {
        return read_from_search;
    }

    public void setRead_from_search(String read_from_search) {
        this.read_from_search = read_from_search;
    }

    public String getName() {
        return name;
    }

    public void setName(String v) {
        name = v;
    }

    public List<AttributeValue> getValues() {
        return values;
    }

    public void setValues(List<AttributeValue> v) {
        values = v;
    }
}
