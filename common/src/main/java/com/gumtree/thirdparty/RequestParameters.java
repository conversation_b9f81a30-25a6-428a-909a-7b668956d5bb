package com.gumtree.thirdparty;

/**
 * RequestParameters are non essential parameters used when creating a tile, that is
 * a default url can be created without them. RequestParameters are both known parameters
 * that are passed to the builder as well as custom parameters that can be added later.
 */
public interface RequestParameters {

    /**
     * @return the parameter names
     */
    Iterable<String> getParameterNames();

    /**
     * @param key the key name
     * @return the value associated with the key
     */
    String getParameter(String key);
}
