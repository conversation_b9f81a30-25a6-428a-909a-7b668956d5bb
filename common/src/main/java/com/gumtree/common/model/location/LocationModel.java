package com.gumtree.common.model.location;

import com.gumtree.domain.location.Location;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Represents a static model of location data.
 */
public interface LocationModel {

    /**
     * Get all locations.
     *
     * @return all locations.
     */
    Collection<Location> getAll();

    /**
     * Get a location by id.
     *
     * @param id the id of the location to retrieve.
     * @return a location by id.
     */
    Location getById(Integer id);

    /**
     * Get a location by name.
     *
     * @param name the name of the location to retrieve.
     * @return a location by name.
     */
    Location getByName(String name);

    /**
     * Get this location's "zoom in" associations.
     *
     * @param location the location to get zoom ins for
     * @return this location's "zoom in" associations.
     */
    Collection<Location> getZoomIn(Location location);

    /**
     * Get this location's "zoom out" associations.
     *
     * @param location the location to get zoom outs for
     * @return this location's "zoom out" associations.
     */
    Collection<Location> getZoomOut(Location location);

    /**
     * Get this location's "nearby" associations.
     *
     * @param location the location to get nearbys for
     * @return this location's "nearby" associations.
     */
    Collection<Location> getNearby(Location location);

    /**
     * Tells whether the specified location has any zoom-in locations, or not
     *
     * @param location the location to check
     * @return true if the location has zoom-in locations
     */
    boolean hasZoomIn(Location location);

    /**
     * Get locations by names
     *
     * @param names names of locations to retrieve
     * @return list of locations matching supplied names
     */
    List<Location> getByNames(String... names);

    /**
     * @param locationIds location ids from search engine
     * @return the location of the ad, smallest = site
     */
    Location getPrimaryLocation(List<Integer> locationIds);

    /**
     * Returns the id of the first location found that is smaller (or the same size as) all of the others
     *
     * @param locationIds the locations to search
     * @return the id of the smallest location found
     */
    Integer getSmallestLocation(Collection<Integer> locationIds);

    /**
     * @return the root location in the overall hierarchy
     */
    Location getRootLocation();

    /**
     * Moved the logic from location service to the location model
     * @param location location you are seeking the county for
     * @return the county or the same location if your already at category level
     */
    Location getCounty(Location location);

    /**
     * This method recognizes if location is county (i.e. L1 category)
     * @param location location you are seeking the county for
     * @return true if location is county
     */
    Boolean isCounty(Location location);

    /**
     * Find all locations with this display name.
     * @param displayName to search for
     * @return all matching locations
     */
    Collection<Location> findByDisplayName(String displayName);

    /**
     * Location hierarchy from root to given location
     */
    Map<Integer, Location> getLocationHierarchy(Location location);

}
