package com.gumtree.common.format;

import com.gumtree.api.PriceFrequency;
import com.gumtree.domain.advert.Price;
import com.gumtree.domain.newattribute.Attribute;

import java.math.BigDecimal;

public interface PriceFormatter {
    String format(Price price, Attribute priceFrequencyAttribute, Long categoryId);

    String format(BigDecimal price, Attribute priceFrequencyAttribute, Long categoryId);

    String format(com.gumtree.api.Price price, PriceFrequency priceFrequency);

    String formatSmall(com.gumtree.api.Price price, PriceFrequency priceFrequency);

    /**
     * @param price to be formatted
     * @return displayable price
     */
    String normalisePrice(Double price);

    String formatConvertingMonthlyToWeekly(BigDecimal price, Attribute priceFrequencyAttribute, Long categoryId);

}