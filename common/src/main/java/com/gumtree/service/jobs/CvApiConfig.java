package com.gumtree.service.jobs;

import com.gumtree.common.properties.GtProps;
import com.gumtree.config.CommonProperty;
import com.gumtree.cvstore.client.config.CvStoreClientFactory;
import com.gumtree.cvstore.spec.CvStoreClient;
import com.gumtree.cvstore.stub.CvStoreStub;
import com.gumtree.cvstore.stub.config.CvStoreStubServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.net.URISyntaxException;

@Configuration
public class CvApiConfig {

    @Bean
    public CvStoreClient cvStoreClient() throws URISyntaxException {
        CvStoreClientFactory factory = new CvStoreClientFactory();
        factory.setHost(GtProps.getStr(CommonProperty.CVSTORE_HOST));
        factory.setPort(GtProps.getInt(CommonProperty.CVSTORE_PORT));
        factory.setVerifySslCertificates(GtProps.getBool(CommonProperty.CVSTORE_VERIFY_SSL_CERTS));
        factory.setConnectionTimeout(GtProps.getInt(CommonProperty.CVSTORE_CONNECTION_TIMEOUT));
        factory.setSocketTimeout(GtProps.getInt(CommonProperty.CVSTORE_SOCKET_TIMEOUT));
        factory.setEnabled(GtProps.getBool(CommonProperty.CVSTORE_ENABLED));
        return factory.create();
    }

    @Bean
    @Lazy(false)
    public CvStoreStub cvStoreStub() throws Exception {
        boolean stubEnabled;
        try {
            stubEnabled = GtProps.getBool(CommonProperty.CVSTORE_STUB_ENABLED);
        } catch (IllegalStateException ex) {
            // I don't expect this property to be set in production so I have to handle that case
            stubEnabled = false;
        }
        CvStoreStubServerFactory factory = new CvStoreStubServerFactory();
        factory.setPort(GtProps.getInt(CommonProperty.CVSTORE_PORT));
        factory.setEnabled(stubEnabled);
        return factory.createAndStartIfEnabled();
    }
}
