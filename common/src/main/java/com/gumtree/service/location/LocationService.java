package com.gumtree.service.location;

import com.gumtree.domain.location.Location;
import com.gumtree.util.model.tree.TreeModel;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * A service for querying available locations.
 *
 * <AUTHOR>
 */
public interface LocationService {

    /**
     * Determine if a location exists by name.
     *
     * @param name the name of the location
     * @return true or false
     */
    boolean exists(String name);

    /**
     * Obtains a location by name.
     *
     * @param name the name of the location
     * @return The location if found; null otherwise
     */
    Location getByName(String name);

    /**
     * Retrieves a location from a given id
     *
     * @param id id of the location
     * @return the location of the id or null if it doesn't exist
     */
    Location getById(Integer id);

    /**
     * Determines if the given location name represents a "landing" location. Example usage appears
     * in the form of location landing pages on the public facing website.
     *
     * @param name the name of the location
     * @return true or false depending on whether the name represents a "landing" location.
     */
    boolean isLanding(String name);

    /**
     * Retrieve a location representing a "landing" location.
     *
     * @param name the name of the location
     * @return the location if it's a landing location; null otherwise
     */
    Location getLanding(String name);

    /**
     * Retrieve the "smallest" landing location in the tree.
     * E.g.: for "Hyde Park" London will be returned, for "Bath" "Bath" will be returned
     *
     * @param location must not be null
     * @return the "smallest" landing location
     */
    Location getLanding(Location location);

    /**
     * Given the current location, get a location tree containing it and it's associated hierarchy.
     *
     * @param currentLocation the current location
     * @return a location tree containing the given location and it's associated hierarchy
     */
    TreeModel<Location> getLocationTree(Location currentLocation);

    /**
     * This method takes a Location as an argument, determines its current county and
     * returns the county Location back
     *
     * @param location current location when called
     * @return a Location object representing the county
     */
    Location getCounty(Location location);

    /**
     * This method recognizes if location is county (i.e. L1 category)
     * @param location
     * @return
     */
    Boolean isCounty(Location location);

    /**
     * Get the given location's "zoom in" associations.
     *
     * @param location the location to get zoom ins for
     * @return this location's "zoom in" associations.
     */
    Collection<Location> getZoomIn(Location location);

    /**
     * Get the given location's "zoom out" associations.
     *
     * @param location the location to get zoom outs for
     * @return this location's "zoom out" associations.
     */
    Collection<Location> getZoomOut(Location location);

    /**
     * Get the given location's "nearby" associations.
     *
     * @param location the location to get nearbys for
     * @return this location's "nearby" associations.
     */
    Collection<Location> getNearby(Location location);

    /**
     * Tells whether the specified location has any zoom-in locations, or not
     *
     * @param location the location to check
     * @return true if the location has zoom-in locations
     */
    boolean hasZoomIn(Location location);

    /**
     * Retrieve a list of locations based on names
     *
     * @param locations array of location names
     * @return A list of locations
     */
    List<Location> getByNames(String... locations);

    /**
     * @param locationIds location ids from search engine
     * @return the location of the ad, smallest = site
     */
    Location getPrimaryLocation(List<Integer> locationIds);


    /**
     * Returns the id of the first location found that is smaller (or the same size as) all of the others
     *
     * @param locationIds the locations to search
     * @return the id of the smallest location found
     */
    Integer getSmallestLocation(Collection<Integer> locationIds);

    /**
     * @return the root location in the overall hierarchy
     */
    Location getRootLocation();

    /**
     * Find all locations with this display name.
     * @param displayName to search for
     * @return all matching locations
     */
    Collection<Location> findByDisplayName(String displayName);

    /**
     * Use this for initialization purposes only. There are a lot of
     * locations and it is never feasible to call this method
     * within a user request.
     *
     * @return all location
     */
    Collection<Location> getAll();

    Map<Integer, Location> getLocationHierarchy(Location location);
}
