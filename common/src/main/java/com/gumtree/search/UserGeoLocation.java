package com.gumtree.search;

import com.gumtree.domain.location.Location;
import org.springframework.util.Assert;

import java.math.BigDecimal;

public class UserGeoLocation {
    public static final double DEFAULT_DISTANCE = 0;
    private double latitude;
    private double longitude;
    private double distance = DEFAULT_DISTANCE;
    private Location location;
    private String postcode;

    public static Builder builder() {
        return new Builder();
    }

    public String getPostcode() {
        return postcode;
    }

    public double getLatitude() {
        return latitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public double getDistance() {
        return distance;
    }

    public Location getLocation() {
        return location;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        UserGeoLocation that = (UserGeoLocation) o;

        return Double.compare(that.distance, distance) == 0
                && Double.compare(that.latitude, latitude) == 0
                && Double.compare(that.longitude, longitude) == 0
                && !(location != null ? !location.equals(that.location) : that.location != null)
                && !(postcode != null ? !postcode.equals(that.postcode) : that.postcode != null);
    }

    @Override
    public int hashCode() {
        int result;
        long temp;
        temp = latitude != +0.0d ? Double.doubleToLongBits(latitude) : 0L;
        result = (int) (temp ^ (temp >>> 32));
        temp = longitude != +0.0d ? Double.doubleToLongBits(longitude) : 0L;
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        temp = distance != +0.0d ? Double.doubleToLongBits(distance) : 0L;
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        result = 31 * result + (location != null ? location.hashCode() : 0);
        result = 31 * result + (postcode != null ? postcode.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "UserGeoLocation{" +
                "latitude=" + latitude +
                ", longitude=" + longitude +
                ", distance=" + distance +
                ", location=" + location +
                ", postcode='" + postcode + '\'' +
                '}';
    }

    public static class Builder {
        private UserGeoLocation instance = new UserGeoLocation();

        public Builder postcode(String postcode) {
            instance.postcode = postcode;
            return this;
        }

        public Builder latitude(BigDecimal latitude) {
            instance.latitude = latitude.doubleValue();
            return this;
        }

        public Builder longitude(BigDecimal longitude) {
            instance.longitude = longitude.doubleValue();
            return this;
        }

        public Builder point(double latitude, double longitude) {
            latitude(BigDecimal.valueOf(latitude));
            longitude(BigDecimal.valueOf(longitude));
            return this;
        }

        public Builder distance(Double distance) {
            if (distance != null) {
                this.instance.distance = distance;
            }
            return this;
        }

        public Builder location(Location location) {
            if (location != null) {
                this.instance.location = location;
            }
            return this;
        }

        public UserGeoLocation build() {
            Assert.notNull(instance.getLatitude());
            Assert.notNull(instance.getLongitude());
            return instance;
        }
    }
}
