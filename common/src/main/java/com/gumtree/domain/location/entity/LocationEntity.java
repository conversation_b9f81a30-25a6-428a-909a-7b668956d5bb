package com.gumtree.domain.location.entity;

import com.gumtree.domain.location.Location;

import java.math.BigDecimal;

/**
 * Represents a location.
 *
 * <AUTHOR>
 */
public class LocationEntity implements Location {

    private Integer id;

    private String name;

    private String displayName;

    private BigDecimal longitude;

    private BigDecimal latitude;

    private BigDecimal radius;


    private boolean isLanding = false;

    /**
     * {@inheritDoc}
     */
    @Override
    public final Integer getId() {
        return id;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String getName() {
        return name;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String getDisplayName() {
        return displayName;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final boolean isLanding() {
        return isLanding;
    }

    @Override
    public BigDecimal getLatitude() {
        return latitude;
    }

    @Override
    public BigDecimal getLongitude() {
        return longitude;
    }

    @Override
    public BigDecimal getRadius() {
        return radius;
    }

    public final void setId(Integer id) {
        this.id = id;
    }

    public final void setName(String name) {
        this.name = name;
    }

    public final void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public final void setLanding(boolean landing) {
        isLanding = landing;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public void setRadius(BigDecimal radius) {
        this.radius = radius;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String toString() {
        return "Location{"
                + "name='" + name + '\''
                + ", displayName='" + displayName + '\''
                + '}';
    }

    /**
     * {@inheritDoc}
     */
    public final int compareTo(Location location) {
        return getId().compareTo(location.getId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        LocationEntity location = (LocationEntity) o;

        if (name != null ? !name.equals(location.name) : location.name != null) {
            return false;
        }

        return true;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final int hashCode() {
        return name != null ? name.hashCode() : 0;
    }

    public static final class Builder {
        private LocationEntity locationEntity;

        private Builder() {
            locationEntity = new LocationEntity();
        }

        public Builder withId(Integer id) {
            locationEntity.id = id;
            return this;
        }

        public Builder withName(String name) {
            locationEntity.name = name;
            return this;
        }

        public Builder withDisplayName(String displayName) {
            locationEntity.displayName = displayName;
            return this;
        }

        public Builder withIsLanding(boolean isLanding) {
            locationEntity.isLanding = isLanding;
            return this;
        }

        public Builder withLongitude(BigDecimal longitude) {
            locationEntity.longitude = longitude;
            return this;
        }

        public Builder withLatitude(BigDecimal latitude) {
            locationEntity.latitude = latitude;
            return this;
        }

        public static Builder entity() {
            return new Builder();
        }

        public LocationEntity build() {
            return locationEntity;
        }
    }
}
