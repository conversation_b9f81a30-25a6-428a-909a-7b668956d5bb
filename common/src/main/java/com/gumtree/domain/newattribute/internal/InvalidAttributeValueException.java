package com.gumtree.domain.newattribute.internal;

/**
 * Exception thrown if we encounter an error creating attribute values. e.g. We
 * expect a numberic value for a particular field, but we end up receieving text
 * instead.
 *
 * <AUTHOR>
 *
 */
public class InvalidAttributeValueException extends Exception {

    private static final long serialVersionUID = 2857759136051500558L;

    /**
     * Constructs an InvalidAttributeValueException with the error message.
     *
     * @param error
     *            - the error message
     */
    public InvalidAttributeValueException(String error) {
        super(error);
    }

    /**
     * Constructs an InvalidAttributeValueException wrapping an Exception.
     *
     * @param e
     *            - The Exception
     */
    public InvalidAttributeValueException(Exception e) {
        super(e);
    }

}
