package com.gumtree.domain;

import org.apache.commons.lang3.Validate;

import javax.annotation.Nonnull;
import java.util.Objects;

/**
 * Represents a page in pagination with 1st page equal to 0.
 */
public class ZeroablePageNumber {
    private final Integer value;

    public static ZeroablePageNumber of(@Nonnull Integer value) {
        return new ZeroablePageNumber(value);
    }

    public static ZeroablePageNumber of(@Nonnull NonZeroPageNumber value) {
        return new ZeroablePageNumber(value.getValue() - 1);
    }

    public ZeroablePageNumber(@Nonnull Integer value) {
        Validate.notNull(value);
        Validate.isTrue(value > -1, "Page must be >= 0");
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    @Override
    public String toString() {
        return "ZeroablePageNumber{" +
                "value=" + value +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ZeroablePageNumber that = (ZeroablePageNumber) o;
        return Objects.equals(value, that.value);
    }

    @Override
    public int hashCode() {

        return Objects.hash(value);
    }
}
