package com.gumtree.domain;

import org.apache.commons.lang3.Validate;

import javax.annotation.Nonnull;
import java.util.Objects;

/**
 * Represents a page in pagination with 1st page equal to 1.
 */
public class NonZeroPageNumber {
    public static final NonZeroPageNumber FIRST_PAGE = NonZeroPageNumber.of(1);

    private final Integer value;

    public static NonZeroPageNumber of(@Nonnull Integer value) {
        return new NonZeroPageNumber(value);
    }

    public NonZeroPageNumber(@Nonnull Integer value) {
        Validate.notNull(value);
        Validate.isTrue(value > 0, "Page must be > 0");
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    @Override
    public String toString() {
        return "NonZeroPageNumber{" +
                "value=" + value +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NonZeroPageNumber that = (NonZeroPageNumber) o;
        return Objects.equals(value, that.value);
    }

    @Override
    public int hashCode() {

        return Objects.hash(value);
    }
}
