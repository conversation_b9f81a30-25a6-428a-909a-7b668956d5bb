package com.gumtree.util.model;

/**
 * Interface for controller url actions.
 */
public interface Action {

    /**
     * Defines the different app endpoints
     */
    enum ApplicationEndpoint {
        BUYER, SELLER, SELLER_SECURE, REPLY, OK_AI
    }

    /**
     * Returns the url of the action
     *
     * @return the url of the action
     */
    String getUrl();

    /**
     * @return the app endpoint
     */
    ApplicationEndpoint getEndpoint();

    /**
     * Determines if Url is absolute.
     *
     * @return if Url is absolute.
     */
    boolean isUrlAbsolute();

    /**
     * Determines if Link is a mail link
     *
     * @return true if a mail link
     */
    boolean isMailLink();
}
