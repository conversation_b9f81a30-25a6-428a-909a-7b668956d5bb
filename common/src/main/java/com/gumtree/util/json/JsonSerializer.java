package com.gumtree.util.json;

import com.google.gson.Gson;

/**
 * A utility class used to convert arbeitry object to and from json representations
 *
 * <AUTHOR>
 */
public final class JsonSerializer {

    //Prents direct instansiation
    private JsonSerializer() {
        //Empty
    }

    /**
     * Gson instance used for conversion
     */
    private static Gson gson = new Gson();

    /**
     * Turns and object into a json string representation
     *
     * @param object The object to serialize
     * @return The json string
     */
    public static String toJsonString(Object object) {
        return gson.toJson(object);
    }

    /**
     * Turns a json string into an instance of an object (which must have a no-arg constructor)
     *
     * @param jsonString The json string
     * @param jsonClass  The class of the object to create
     * @param <T>        The type of the object to create
     * @return The newly created object
     */
    public static <T> T toObject(String jsonString, Class<T> jsonClass) {
        return gson.fromJson(jsonString, jsonClass);
    }

}
