package com.gumtree.util.collection;

import java.util.ArrayList;
import java.util.List;

/**
 * Composite object for storing returned result set and total available result count
 *
 * @param <T> Type of object in list
 */
public class PaginationList<T> {

    private int totalSize;
    private List<T> list;

    /**
     * Constructor. Accepts a typed list and total
     *
     * @param list result set returned
     * @param total number of available results
     */
    public PaginationList(List<T> list, int total) {
        this.list = list;
        this.totalSize = total;
    }

    /**
     * Constructor for an empty list and zero total size
     */
    public PaginationList() {
        this.list = new ArrayList<T>();
        this.totalSize = 0;
    }

    /**
     * Constructor for a list where the total size is the same size as the list
     * @param list list for composite object
     */
    public PaginationList(List<T> list) {
        this.list = list;
        this.totalSize = list.size();
    }

    public final int getTotalSize() {
        return totalSize;
    }

    public final List<T> getResults() {
        return list;
    }

    public final boolean isEmpty() {
        return list == null || list.isEmpty();
    }

    /**
     * Calls the sublist function on the list and sets the sublist to this list
     *
     * @param start start index of the list
     * @param size size of the list
     */
    public final void subList(int start, int size) {
        this.list = list.subList(start, size);
    }

    public final int getResultsSize() {
        return list.size();
    }

}