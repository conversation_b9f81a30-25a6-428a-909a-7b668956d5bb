package com.gumtree.util.mime;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

public enum AcceptedMime {
    DOC("application/msword"),
    RTF("application/rtf"),
    PDF("application/pdf"),
    DOC_X("application/vnd.openxmlformats-officedocument.wordprocessingml.document");

    private final String contentType;

    // Reverse-lookup map for getting an AcceptedMime from its contentType
    private static final Map<String, AcceptedMime> CONTENT_LOOKUP = Arrays
            .stream(values())
            .collect(Collectors.toMap(AcceptedMime::getContentType, Function.identity()));

    AcceptedMime(String contentType) {
        this.contentType = contentType;
    }

    public String getContentType() {
        return contentType;
    }

    public static Optional<AcceptedMime> fromContent(String contentType){
        if (CONTENT_LOOKUP.contains<PERSON><PERSON>(contentType)){
            return Optional.of(CONTENT_LOOKUP.get(contentType));
        }
        return Optional.empty();
    }
}
