package com.gumtree.util.cache.memcached;

import com.gumtree.util.cache.CacheNamespace;
import com.gumtree.util.cache.CacheService;
import com.gumtree.util.cache.CacheValueFactory;
import net.spy.memcached.CASValue;

import java.util.Date;

/**
 * User: rajisingh
 * Date: 19/04/11
 * Time: 16:55
 *
 * @param <T> the type of the object that this service caches
 */
public class MemcachedCacheService<T> extends AbstractMemcachedCacheService<T> implements CacheService<T> {

    // TODO: Use async memcached calls and handle failure scenarios

    private CacheValueFactory<T> valueFactory;

    /**
     * Set the value factory for this service to generate values with
     *
     * @param factory the factory to use
     */
    public final void setValueFactory(CacheValueFactory<T> factory) {
        valueFactory = factory;
    }

    /**
     * Get the value for the specified key.  If the cache has a fresh value, use that.
     * If the cache has a non-stale value that needs refreshing, acquire a lock and refresh it
     * or if the lock fails, use the old value.  If the cache is completely stale synchronously
     * get a new value
     *
     * @param namespace must not be null
     * @param key the key for the desired value
     * @return the value for the specified key
     */
    @Override
    public final T get(CacheNamespace namespace, String key) {
        CASValue<T> casValue = getFromCache(namespace, key);

        if (casValue == null) {
            // TODO: Negotiate a single updater for initialisation as well, with more limited timeouts
            // TODO: to prevent clients from blocking for too long
            // TODO: This is to prevent update storms at initialisation
            LOGGER.debug("No cache value available, fetching a new one");
            return updateEntry(namespace, new MemcachedEntry<T>(key)).getValue();
        }

        // TODO: Handle incompatible data exceptions
        MemcachedEntry<T> cacheEntry = (MemcachedEntry<T>) casValue.getValue();

        if (cacheEntry == null) {
            cacheEntry = new MemcachedEntry<T>(key);
        }

        if (needsUpdating(cacheEntry)) {
            if (becomeEntryUpdater(namespace, casValue)) {
                LOGGER.debug("Acquired update 'lock', updating entry: " + cacheEntry.getKey());
                return updateEntry(namespace, cacheEntry).getValue();
            }

            LOGGER.debug("Couldn't acquire update 'lock', using old value: " + cacheEntry.getKey());
        }

        // Fall back on return the existing value
        return cacheEntry.getValue();
    }

    private MemcachedEntry<T> updateEntry(CacheNamespace namespace, MemcachedEntry<T> cacheEntry) {
        cacheEntry.setValue(valueFactory.create(cacheEntry.getKey()));
        cacheEntry.setUpdated(new Date());
        memcachedClient.set(makeCacheKey(namespace, cacheEntry.getKey()), cacheExpiry, cacheEntry);

        return cacheEntry;
    }

}
