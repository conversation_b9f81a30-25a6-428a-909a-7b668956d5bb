package com.gumtree.util.url;

import com.gumtree.api.Ad;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.advert.Advert;
import com.gumtree.domain.location.Location;
import com.gumtree.domain.media.Image;
import com.gumtree.domain.media.ImageSize;
import com.gumtree.util.model.Action;

import java.util.Map;

/**
 * A component for generating urls linking to domain entity views.
 */
public interface UrlScheme {

    /**
     * Get a url for viewing a location.
     *
     * @param location the location to view.
     * @return a url for viewing a location.
     */
    String urlFor(Location location);

    /**
     * Gets a url for a location from a location id,
     * handy when you have access to the urlScheme but not
     * the locationService and the users cookies
     *
     * @param locationId typically from a cookie
     * @return a url
     */
    String urlForLocationId(Integer locationId);

    /**
     * Handy method to return the homepage so its not hardcoded everywhere
     *
     * @return url for the homepage
     */
    String urlForHomepage();

    /**
     * Get a url for viewing a category landing page
     *
     * @param category the category to view
     * @param location the location selected alongside the category
     * @return a url for viewing a category landing page.
     */
    String urlForCategory(Category category, Location location);

    /**
     * Generate the listing page url
     *
     * @param category category
     * @param location location
     * @return a listing page url
     */
    String listingUrlForCategory(Category category, Location location);

    /**
     * Get the url for viewing the "search postings" page.
     *
     * @param searchTerms    terms given by user
     * @param searchLocation location given by user
     * @return the url
     */
    String urlForSearchPostings(String searchTerms, String searchLocation);

    /**
     * Get a url for viewing an advert
     *
     * @param advert the avertisement to view
     * @return a url for viewing an advert
     */
    String urlFor(Advert advert);


    /**
     * Get a url for an Ad object.
     *
     * @param ad the ad
     * @return the url
     */
    String urlFor(Ad ad);

    /**
     * Get an edit url for an Ad object.
     *
     * @param ad the ad
     * @return the url
     */
    String editUrlFor(Ad ad);

    /**
     * Get a restore url for an Ad object.
     *
     * @param ad the ad
     * @return the url
     */
    String restoreUrlFor(Ad ad);

    /**
     * Get a url for viewing an advert
     *
     * @param advertId    the avertisement to view
     * @param advertTitle the title
     * @param categoryId  the category
     * @return a url for viewing an advert
     */
    String urlForAdvert(Long advertId, String advertTitle, Long categoryId);

    /**
     * Get the different size urls for an image
     *
     * @param image the image to generate image urls for.
     * @return a map of the image urls
     */
    Map<ImageSize, String> urlsFor(Image image);

    /**
     * Get an image url for an advert thumbnail
     *
     * @param image the advertisement image url
     * @return the url of the thumbnail for the advert
     */
    String thumbnailUrlFor(Image image);

    /**
     * Get an image url for an advert thumbnail
     *
     * @param imageBaseUrl the advertisement image url
     * @return the url of the thumbnail for the advert
     */
    String thumbnailUrlFor(String imageBaseUrl);

    /**
     * Get the url for previewing an image
     *
     * @param image the image
     * @return the url
     */
    String previewUrlFor(Image image);

    /**
     * Get the url for previewing an image
     *
     * @param imageBaseUrl the image
     * @return the url
     */
    String previewUrlFor(String imageBaseUrl);

    /**
     * Returns a url for given action
     *
     * @param action action to return url for
     * @return url for action
     */
    String urlFor(Action action);

    /**
     * Returns the bushfire post ad url.
     *
     * @param category the category (can be null).
     * @return bushfire post ad url
     */
    String bushfirePostAdUrlFor(Category category);

    /**
     * Returns the bushfire post ad url.
     *
     * @param categoryId id of category
     * @return bushfire post ad url.
     */
    String bushfirePostAdUrlForCategoryId(Long categoryId);

    /**
     * @return plain legacy post ad url.
     */
    String postAdUrl();

    /**
     * Returns an url to post an ad for the given location
     *
     * @param location location to return the post ad url for
     * @return url for the post ad
     */
    String postAdUrlFor(Location location);

    /**
     * Returns an url to post an ad for the given category
     *
     * @param category category to return the post ad url for
     * @return url for the post ad
     */
    String postAdUrlFor(Category category);

    /**
     * Returns an url to post an ad for the given location and category
     *
     * @param location location to return the post ad url for
     * @param category category to return the post ad url for
     * @return url for the post ad
     */
    String postAdUrlFor(Location location, Category category);

    /**
     * Returns an url to post an event for the given location
     *
     * @param location location to return the post event url for
     * @return url for the post event
     */
    String postEventUrlFor(Location location);

    /**
     * @return the bushfire post event url
     */
    String bushfirePostEventUrl();

    /**
     * Returns an url to view all events for the given location
     *
     * @param location location to return the view events url for
     * @return url for the view events
     */
    String viewEventsUrlFor(Location location);

    /**
     * Returns an rss url to view all events for the given location
     *
     * @param location location to return the view events rss url for
     * @return url rss for the view events
     */
    String rssEventsUrlFor(Location location);

    /**
     * Returns an url to the "create an email alert" page.
     *
     * @param categoryId maybe null
     * @param location   may be null
     * @return the url
     */
    String emailAlertsUrlFor(Long categoryId, Location location);

    /**
     * Returns an url to the "save search" ajax service.
     *
     * @return the url
     */
    String saveSearchUrl();

    /**
     * Returns the activate email alert end point
     * @return the url
     */
    String saveEmailAlertUrl();

    /**
     * Returns a url to the reply ad form page.
     *
     * @param advert the advert
     * @return a url to the reply ad form page.
     */
    String replyUrlFor(Advert advert);

    /**
     * Returns a url for an XML sitemap page using the given path prefix.
     *
     * @param pathPrefix path prefix to use
     * @param page       page number
     * @return the url
     */
    String urlForXMLSitemap(String pathPrefix, int page);

    /**
     * Return a thumbnail url for a given youtube video URL.
     *
     * @param youtubeUrl link to the actual video
     * @return the thumbnail url
     */
    String urlForYoutubeThumbnail(String youtubeUrl);

    /**
     * Return a url for embedding a given youtube video into the page.
     *
     * @param youtubeUrl link to the actual video
     * @return the embed url
     */
    String urlForYoutubeEmbed(String youtubeUrl);

    /**
     * Return a url for getting package usage history.
     *
     * @param packageId the id of the package
     * @return a url for getting package usage history.
     */
    String urlForPackageUsageHistory(long packageId);

    /**
     * Return a url for all adverts by a seller
     *
     * @param originalAdvertId the advert id to get all for
     * @return a url for the seller ads page
     */
    String urlForSellerAds(long originalAdvertId);

    /**
     * Return a base url to use to report an advert
     *
     * @param advertId the advert id
     * @return a base url for ad reporting by user
     */
    String urlToReportAd(long advertId);

    String urlforPredictCate();

    String urlforTitleSuggest();

    String urlforDescSuggest();

    String urlforAttributeSuggest();
}
