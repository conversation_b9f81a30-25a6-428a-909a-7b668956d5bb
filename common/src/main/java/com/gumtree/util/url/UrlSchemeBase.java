package com.gumtree.util.url;

import com.gumtree.common.properties.GtProps;
import com.gumtree.util.model.Action;

import static com.gumtree.util.model.Action.ApplicationEndpoint.BUYER;
import static org.springframework.util.StringUtils.hasLength;

/**
 * Provides base functionality for URL scheme classes.
 */
public abstract class UrlSchemeBase {
    private String buyerBaseUri = GtProps.getStr("gumtree.url.buyer.base_uri");
    private String sellerBaseUri = GtProps.getStr("gumtree.url.seller.base_uri");
    private String sellerSecureBaseUri = GtProps.getStr("gumtree.url.seller.secure.base_uri");
    private String replyBaseUri = GtProps.getStr("gumtree.url.reply.base_uri");
    private String okAIBaseUri = GtProps.getStr("gumtree.url.ok_ai.base_uri");

    /**
     * @return an absolute URL builder for HTTP urls.
     */
    protected final StringBuilder absoluteUrlBuilder() {
        return absoluteUrlBuilder(BUYER);
    }

    /**
     * Create an absolute URL builder.
     *
     * @param endpoint determines the application endpoint
     * @return an absolute URL builder.
     */
    protected final StringBuilder absoluteUrlBuilder(Action.ApplicationEndpoint endpoint) {

        switch (endpoint) {
            case SELLER:
                return getStringBuilder(sellerBaseUri);
            case SELLER_SECURE:
                return getStringBuilder(sellerSecureBaseUri);
            case REPLY:
                return getStringBuilder(replyBaseUri);
            case OK_AI:
                return getStringBuilder(okAIBaseUri);
            default:
                return getStringBuilder(buyerBaseUri);
        }
    }

    private StringBuilder getStringBuilder(String baseUri) {
        return hasLength(baseUri) ? new StringBuilder(baseUri) : new StringBuilder();
    }
}
