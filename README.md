# Seller

Seller is primarily front-end service generating HTML pages for seller site of the website. This includes:
- logging in/logging out
- registration
- ad posting/management (including SYI page)
- account/profile management
- message centre (web based messaging)

Application has to be deployed in conjunction with buyer app to provide complete functionality of the website.

## Jenkins

- [Server](https://jenkins.ci.gt.ecg.so/view/Delivery_Pipeline/) -> click _Run_ -> edit _BUILD_BRANCH_ and _QA_ENVIRONMENT_
  -> click _Add releasable_ in _RELEASABLES_ section -> select _Seller_ -> click _Build_
- [Responsive_Seller_QA_Tests](https://jenkins.ci.gt.ecg.so/job/Responsive_Seller_QA_Tests/)
- [Responsive_Seller_Sanity_Pack_QA_Tests](https://jenkins.ci.gt.ecg.so/job/Responsive_Seller_Sanity_Pack_QA_Tests/)

## Running locally

- Set project JDK to 1.8 and <PERSON><PERSON> to 2.11.8
- Make sure your `JAVA_HOME` points to Java 1.8
- Build the project: `mvn -T 3C clean compile`
- Make sure [gumtree-frontend](https://github.es.ecg.tools/gumtree/gumtree-frontend) is cloned to the same directory as `seller`
  (see `gumtree.web.templates.path` property in [seller-server.properties](server/src/main/resources/seller-server.properties))
- Configure `envname` property if `staging` QA environment is not the one you require
  (see [seller-server.properties](server/src/main/resources/seller-server.properties))
- Configure `gumtree.http.port` property if `8181` is not the one you require
  (see [seller-server.properties](server/src/main/resources/seller-server.properties))
- Make sure `telepresence` is connected to
  GCP (see [local development in GCP](https://github.es.ecg.tools/uk-architecture/documentation/blob/master/guidelines/Gumtree/005_local-development-in-gcp.md))
- Run [GumtreeJettyMain](server/src/main/java/com/gumtree/GumtreeJettyMain.java) with module set to `server` and `Working directory` set
  to `server` in IntelliJ's Run/Debug configuration

### Using a Forward Proxy into an environment
As an alternative to using telepresence, you can use a 'forward proxy' which allows you access to various services
provided you're on the VPN.  The majority of configuration is already contained within this project for ease of running,
but there are some additional configuration steps which need to be performed in order to use this solution.

#### Setup HTTPS to HTTP Proxying
The forward proxy runs over HTTPS, but the Category API and Payment API only allow for HTTP connections, therefore we
need to have a local forward proxy that terminates on HTTP.

*Run a pre-configured NGINX instance*
```
cd dev-configuration
docker-compose up
```

*Configure your /etc/hosts file*
Ensure the following items are added to your /etc/hosts file.  These URLs are within the environment configuration
(e.g. `seller-server-staging-proxy.properties`), and the NGINX instance uses a regex expression on the host to
determine which service to route to.
```
127.0.0.1	category-api.staging.localhost-proxy.gumtree.io
::1     	category-api.staging.localhost-proxy.gumtree.io

127.0.0.1	category-api.zoidberg.localhost-proxy.gumtree.io
::1     	category-api.zoidberg.localhost-proxy.gumtree.io

127.0.0.1	payment-api.staging.localhost-proxy.gumtree.io
::1     	payment-api.staging.localhost-proxy.gumtree.io

127.0.0.1	payment-api.zoidberg.localhost-proxy.gumtree.io
::1     	payment-api.zoidberg.localhost-proxy.gumtree.io
```

To test, fire a GET request at `http://category-api.staging.localhost-proxy.gumtree.io:8443/api/categories`, and it
should return the list of categories.

If it doesn't work :-

- Ensure you're on the VPN.
- Ensure the NGINX instance is running via Docker.
- Ensure your /etc/hosts file has been updated.
- 
#### Run Seller with the forwardProxy flag.
All configuration required to run with the forward proxy is contained within this repository.  There should be no
need to make any changes to any properties files to get this running - it *should* be as simple as setting the correct
environment variable.

Within IntelliJ, update the run configuration for 'GumtreeJettyMain', and specify the following within the CLI Arguments
section: `-DwithForwardProxy=staging`.  This is enough to force the application to use the
`seller-server-staging-proxy.properties` file.

Again - there is no need to have any additional environment based configuration within any of the properties files, or
in the run configuration.  It's intended Seller runs 'out the box' (provided the NGINX instance and hosts changes have
been made).

## Development

- Debugging model in JSON format [see](doc/display_model_README.md)
- Edit FTL templates [see](https://github.es.ecg.tools/gumtree/gumtree-frontend)
- Edit static assets [see](https://github.es.ecg.tools/gumtree/frontend). Note: the path to all static assets has a prefix of "/1" as this
  is a structure agreed with SiteOps to distinguish static assets of different apps.
- Prometheus monitoring metrics produced by service: `curl localhost:8080/internal/metrics`

### Tips:

#### Logging-in
You can use predefined users or create new one if needed (See [test data service contract](https://github.es.ecg.tools/gumtree/gumtree-test-data-service/blob/master/contract/contract.yaml))

Example request:
```
curl --location --request POST 'http://integration.gt-adtech.ams1.cloud/users' \
--header 'Content-Type: application/json' \
--data-raw '{
    "emailAddress": "<EMAIL>",
    "id": -1,
    "accountId": 1,
    "password": "Gumtree123!",
    "firstName": "UserAbc",
    "proUser": true,
    "replyType": null,
    "trustedReporter": false,
    "telephoneNumber": "***********",
    "role": null,
    "activate": true,
    "phoneVerified": true,
    "deactivate": false,
    "blacklisted": false,
    "watchlisted": false,
    "postingSince": -1,
    "cv": null,
    "crm": null,
    "accountVat": null,
    "accountLogoUrl": null
}'
```

#### Posting a car
See supported plate numbers in [third party mocks](https://github.es.ecg.tools/gumtree/thirdparty-mocks/tree/master/src/vrm/responses)

Example plate number `LB57ZLV`

#### Payment
See test user credentials in [wiki page](https://wiki.es.ecg.tools/display/GumtreeUK/Test+user+credentials)

Example PayPal user / password: `<EMAIL>` / `gumtree123`

## Main classes/files:

- PostAd controller - [PostAdSubmitController](server/src/main/java/com/gumtree/web/seller/page/postad/controller/PostAdSubmitController.java)
- PostAd Bump Up controller: final step of posting flow - [PostAdBumpUpController](server/src/main/java/com/gumtree/web/seller/page/postad/controller/PostAdBumpUpController.java)
- General interface for steps executed during posting ad process - [PostAdStep](server/src/main/java/com/gumtree/web/seller/page/postad/controller/steps/PostAdStep.java)
- AdvertEditor: holds state of advert that is being posted, at any point user can post multiple adverts - [AdvertEditorImpl](server/src/main/java/com/gumtree/web/seller/page/postad/model/AdvertEditorImpl.java)
- PostAd workspace: manages advert editors - [PostAdWorkspaceImpl](server/src/main/java/com/gumtree/web/seller/service/PostAdWorkspaceImpl.java)
- File controlling presentation of attributes on posting flow - [attribute-presentation-metadata.json](server/src/main/resources/META-INF/config/attributes/attribute-presentation-metadata.json)

## Application release checks

Seller is deployed in the AMS1 and DUS1 clouds

- SSH: go to [Production Goto Page](http://goto.gt.ecg.so) to find nodes. Use [Puppet](https://puppetboard.gt.ecg.so/nodes) and search for
  *gt-seller*
- [Sentry](https://sentry.gt.ecg.so/gumtree/seller-server/)
- [Kibana application logs](https://kibana.gtuk-prod-logging.elastic.ams1.cloud/app/kibana#/discover?_g=%28%29&_a=%28columns:!%28_source%29,index:AV_95dWNDeZJW40GdrNt,interval:auto,query:%28query_string:%28query:'instance:%20seller'%29%29,sort:!%28'@timestamp',desc%29%29)
- [Kibana access logs](https://kibana.gtuk-prod-logging.elastic.ams1.cloud/app/kibana#/discover?_g=%28%29&_a=%28columns:!%28_source%29,index:AV_95ahcQAFNf-p6FZ9a,interval:auto,query:%28query_string:%28analyze_wildcard:!t,query:'instance:%20seller'%29%29,sort:!%28'@timestamp',desc%29%29)
- [Grafana 'Seller' Dashboard](https://grafana.gt.ecg.so/d/te2VH5iZz/seller?orgId=1)
- [Grafana 'Hystrix Dashboard'](https://grafana.gt.ecg.so/d/7zPknx9Wz/hystrix-dashboard?orgId=1), select `seller-service`
- Run [Responsive_Seller_Sanity_Pack_QA_Tests](https://jenkins.ci.gt.ecg.so/job/Responsive_Seller_Sanity_Pack_QA_Tests/)

## Integrations

### Madgex integration

Jobs vertical at some point in the past was extracted from Gumtree and moved to Madgex job boards. Currently we partially brought it back:

- seller/recruiter website is managed by Madgex - on QA, it can be tested on gt-demo (user: madgexUAT.Gumt, password: sjdigfoedjDFDUJN!j)
- seller/job seeker is on GT - adverts are imported into ES
  by [madgex-advert-importer](https://github.es.ecg.tools/gumtree/madgex-advert-importer)

Job related features implemented in seller

- srp, vip: mostly in the same way as we do it for other verticals
- applying for jobs: we send application request to Madgex ([doc](../doc/GumtreeJobs-ApplicationAPI-141019-0923-4.pdf)) and job-applications
  service ([job-applications](https://github.es.ecg.tools/gumtree/job-applications))
- message centre: we merge conversations coming from Comas with job applications saved in above mentioned job-applications service
- vip metrics recording/insights ([doc](../doc/Analytics Sessions Web Integration Documentation v0.2.0.pdf))

### Google Analytics

The dataLayer -> Custom Dimension mapping can be found [here](https://wiki.es.ecg.tools/display/ANYL/Custom+Dimensions)
And the GTM macro powering, it can be
found [here](https://github.es.ecg.tools/eCGAnalytics/analyticsTracking/blob/master/googleTagManager/macros/universalAnalytics/UA_Set_Dimensions.js)
