package com.gumtree.api;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.util.List;

public class OkHttpClientMetricsInterceptor implements Interceptor {

    private final MeterRegistry registry;

    public OkHttpClientMetricsInterceptor(MeterRegistry registry) {
        this.registry = registry;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Timer.Sample sample = Timer.start(registry);
        try {
            Response response = chain.proceed(request);
            sample.stop(Timer.builder("http.requests")
                    .tag("method", request.method())
                    .tag("uri", extractPath(request.url()))
                    .tag("host", extractHost(request.url()))
                    .register(registry));
            return response;
        } catch (IOException e) {
            sample.stop(Timer.builder("http.requests")
                    .tag("method", request.method())
                    .tag("uri", extractPath(request.url()))
                    .tag("host", extractHost(request.url()))
                    .tag("error", "true")
                    .register(registry));
            throw e;
        }
    }

    static String extractPath(HttpUrl url) {
        List<String> segments = url.pathSegments();
        if (segments == null || segments.isEmpty()) {
            return "-";
        }
        if (segments.size() > 1 && "api".equalsIgnoreCase(segments.get(0))) {
            return "/api/" + segments.get(1);
        }
        return "/" + segments.get(0);
    }

    static String extractHost(HttpUrl url)  {
        String host = url.host();
        if (host == null) {
            return "-";
        }
        String[] segments = host.split("[/.]");
        return segments[0];
    }
}
