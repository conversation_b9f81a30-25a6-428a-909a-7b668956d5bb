package com.gumtree.web.common.domain.messagecentre;


import java.util.Objects;

public final class Conversation {

    private String id;
    private String converseeName;
    private String converseeId;
    private boolean unread;
    private Message lastMessage;
    private Role userRole;

    public static Builder builder() {
        return new Builder();
    }

    private Conversation(Builder builder) {
        this.id = builder.id;
        this.converseeName = builder.converseeName;
        this.converseeId = builder.converseeId;
        this.unread = builder.unread;
        this.lastMessage = builder.lastMessage;
        this.userRole = builder.userRole;
    }

    public String getId() {
        return id;
    }

    public String getConverseeName() {
        return converseeName;
    }

    public String getConverseeId() {
        return converseeId;
    }

    public boolean isUnread() {
        return unread;
    }

    public Message getLastMessage() {
        return lastMessage;
    }

    public Role getUserRole() {
        return userRole;
    }

    public static class Builder {
        private String id;
        private String converseeName;
        private String converseeId;
        private boolean unread;
        private Message lastMessage;
        private Role userRole;

        public Builder setId(String id) {
            this.id = id;
            return this;
        }

        public Builder setConverseeName(String converseeName) {
            this.converseeName = converseeName;
            return this;
        }

        public Builder setConverseeId(String converseeId) {
            this.converseeId = converseeId;
            return this;
        }

        public Builder setUnread(boolean read) {
            this.unread = read;
            return this;
        }

        public Builder setLastMessage(Message lastMessage) {
            this.lastMessage = lastMessage;
            return this;
        }

        public Builder setUserRole(Role userRole){
            this.userRole = userRole;
            return this;
        }

        public Conversation build() {
            return new Conversation(this);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Conversation that = (Conversation) o;
        return unread == that.unread &&
                Objects.equals(id, that.id) &&
                Objects.equals(converseeName, that.converseeName) &&
                Objects.equals(converseeId, that.converseeId) &&
                Objects.equals(lastMessage, that.lastMessage) &&
                userRole == that.userRole;
    }

    @Override
    public int hashCode() {

        return Objects.hash(id, converseeName, converseeId, unread, lastMessage, userRole);
    }

    @Override
    public String toString() {
        return "Conversation{" +
                "id='" + id + '\'' +
                ", converseeName='" + converseeName + '\'' +
                ", converseeId='" + converseeId + '\'' +
                ", unread=" + unread +
                ", lastMessage=" + lastMessage +
                ", userRole=" + userRole +
                '}';
    }
}
