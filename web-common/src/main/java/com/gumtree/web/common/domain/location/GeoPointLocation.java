package com.gumtree.web.common.domain.location;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.math.BigDecimal;
import java.util.Optional;

public interface GeoPointLocation {
    Optional<BigDecimal> getLatitude();

    Optional<BigDecimal> getLongitude();

    @JsonIgnore
    @org.codehaus.jackson.annotate.JsonIgnore
    default BigDecimal getNotNullLatitude() {
        return getLatitude().orElseThrow(UnsupportedOperationException::new);
    }

    @JsonIgnore
    @org.codehaus.jackson.annotate.JsonIgnore
    default BigDecimal getNotNullLongitude() {
        return getLongitude().orElseThrow(UnsupportedOperationException::new);
    }
}
