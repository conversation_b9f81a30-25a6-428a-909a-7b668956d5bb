package com.gumtree.web.common.domain.order.converter;

import com.google.common.base.Optional;
import com.gumtree.api.Ad;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.ApiOrderItem;
import com.gumtree.web.common.domain.order.Order;
import com.gumtree.web.common.domain.order.entity.OrderEntity;
import com.gumtree.web.common.domain.order.entity.OrderItemEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 */
@Component
public class DefaultApiOrderToOrderEntityConverter implements ApiOrderToOrderEntityConverter {

    @Autowired
    private BushfireApi bushfireApi;

    @Autowired
    private CategoryModel categoryModel;

    @Override
    public final Order convert(ApiOrder apiOrder) {
        OrderEntity order = new OrderEntity();
        order.setId(apiOrder.getId());
        order.setAccountId(apiOrder.getAccountId());
        order.setTotalVat(convertPenceToPounds(apiOrder.getTotalVat()));
        order.setTotalIncVat(convertPenceToPounds(apiOrder.getTotalIncVat()));
        order.setTotalExcVat(convertPenceToPounds(apiOrder.getTotalIncVat() - apiOrder.getTotalVat()));
        List<OrderItemEntity> items = new ArrayList<OrderItemEntity>();
        order.setItems(items);
        AdvertApi advertApi = bushfireApi.create(AdvertApi.class);
        for (ApiOrderItem apiOrderItem : apiOrder.getItems()) {
            OrderItemEntity item = new OrderItemEntity();
            Ad advert = advertApi.getAdvert(apiOrderItem.getAdvertId());
            item.setAdvert(advert);
            item.setProductName(apiOrderItem.getProductName());
            item.setPaymentDetail(apiOrderItem.getPaymentDetail());
            item.setStatus(apiOrderItem.getStatus());
            item.setPriceIncVat(convertPenceToPounds(apiOrderItem.getPriceIncVat()));
            Optional<Category> category = categoryModel.getCategory(advert.getCategoryId());
            if (category.isPresent()) {
                item.setCategory(category.get());
            }
            items.add(item);
        }
        return order;
    }

    private BigDecimal convertPenceToPounds(Long pence) {
        if (pence != null) {
            return new BigDecimal(pence)
                    .divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_EVEN);
        } else {
            return BigDecimal.ZERO;
        }
    }
}
