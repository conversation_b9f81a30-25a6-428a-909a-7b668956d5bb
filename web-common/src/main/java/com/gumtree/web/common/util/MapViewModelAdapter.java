package com.gumtree.web.common.util;

import java.util.Map;

/**
 */
public final class MapViewModelAdapter implements ViewModelAdapter {

    private Map<String, Object> map;

    /**
     * Constructor.
     *
     * @param map the wrapped model
     */
    public MapViewModelAdapter(Map<String, Object> map) {
        this.map = map;
    }

    @Override
    public void addObject(String id, Object object) {
        map.put(id, object);
    }
}
