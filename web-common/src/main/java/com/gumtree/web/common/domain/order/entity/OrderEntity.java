package com.gumtree.web.common.domain.order.entity;

import com.gumtree.api.domain.payment.ApiPaymentDetail;
import com.gumtree.web.common.domain.order.Order;
import com.gumtree.web.common.domain.order.OrderItem;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class OrderEntity implements Order {

    private Long id;

    private List<OrderItemEntity> items;

    private Long accountId;

    private BigDecimal totalVat;

    private BigDecimal totalIncVat;

    private BigDecimal totalExcVat;

    @Override
    public List<OrderItemAd> getAds() {
        return getItems()
                .stream()
                .collect(Collectors.groupingBy(i -> i.getAdvert().getId()))
                .entrySet()
                .stream()
                .map(this::toOrderItemAd)
                .collect(Collectors.toList());
    }

    private OrderItemAd toOrderItemAd(final Map.Entry<Long, List<OrderItem>> item) {
        OrderItemAd.Builder builder = OrderItemAd.builder();
        return item.getValue().stream().findFirst()
                .map(OrderItem::getAdvert)
                .map(a -> builder
                        .withId(a.getId())
                        .withTitle(a.getTitle())
                        .withLocation(a.getSmallestLocation().getName())
                        .withProducts(toProducts(item.getValue()))
                        .build())
                .orElse(builder.build());
    }

    private List<OrderItemProduct> toProducts(final List<OrderItem> items) {
        return items.stream()
                .map(i -> new OrderItemProduct(
                        i.getProductName(),
                        i.getPriceIncVat(),
                        Optional.ofNullable(i.getPaymentDetails()).map(ApiPaymentDetail::getPaymentMethod).orElse(null))
                )
                .collect(Collectors.toList());
    }

    @Override
    public final List<OrderItem> getItems() {
        return new ArrayList<>(items);
    }

    @Override
    public final Long getAccountId() {
        return accountId;
    }

    @Override
    public final Long getId() {
        return id;
    }

    public final void setId(Long id) {
        this.id = id;
    }

    public final void setItems(List<OrderItemEntity> items) {
        this.items = items;
    }

    public final void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public final BigDecimal getTotalVat() {
        return totalVat;
    }

    public final void setTotalVat(BigDecimal totalVat) {
        this.totalVat = totalVat;
    }

    public final BigDecimal getTotalIncVat() {
        return totalIncVat;
    }

    public final void setTotalIncVat(BigDecimal totalIncVat) {
        this.totalIncVat = totalIncVat;
    }

    public final BigDecimal getTotalExcVat() {
        return totalExcVat;
    }

    public final void setTotalExcVat(BigDecimal totalExcVat) {
        this.totalExcVat = totalExcVat;
    }

    @Override
    public final Boolean isFree() {
        for (OrderItem orderItem : items) {
            if (!orderItem.isFree()) {
                return false;
            }
        }
        return true;
    }
}
