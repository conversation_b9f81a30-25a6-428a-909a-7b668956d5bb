package com.gumtree.web.common.page.context;

import com.google.common.base.Preconditions;
import com.gumtree.api.User;
import com.gumtree.api.category.domain.Category;
import com.gumtree.common.util.StringUtils;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.legacy.LegacySite;
import com.gumtree.legacy.LegacySiteMap;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.helper.DisplayAdsViewMode;
import com.gumtree.web.abtest.AbTestType;
import com.gumtree.web.common.domain.order.Order;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.common.page.handler.PageHandler;
import com.gumtree.web.common.page.model.PageModelFactory;
import com.gumtree.web.common.util.ExecutorServiceFactory;
import com.gumtree.zeno.core.domain.PageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mobile.device.Device;
import org.springframework.mobile.device.DeviceResolver;
import org.springframework.mobile.device.LiteDeviceResolver;
import org.springframework.web.util.HtmlUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * Common implementation of {@link GumtreePageContext} used by public web facing applications.
 */
public abstract class GumtreeCommonPageContext<T> implements RequestScopedGumtreePageContext<T> {

    @Autowired
    private LocationService locationService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private PageModelFactory pageModelFactory;

    @Autowired
    private ExecutorServiceFactory executorServiceFactory;

    @Autowired
    private LegacySiteMap legacySiteMap;

    private ExecutorService executorService;
    private PageType pageType;
    private boolean initialised = false;
    private URL absoluteRequestUrl;
    private String referrer;
    private Location homepageLocation;
    private Location location;
    private Category category;
    private Map<Integer, Category> categoryLevelHierarchy;
    private Order order;
    private User user;
    private String searchTerm;
    private Boolean hasContent;
    private DisplayAdsViewMode displayAdsViewMode;
    private Integer currentPageNumber;
    private String url;
    private List<AbTestType> abTestTypes;
    private T model;
    private String userIputLocation;
    private String deviceType;
    private static DeviceResolver deviceResolver = new LiteDeviceResolver();
    private HttpServletRequest request;
    private HttpServletResponse response;
    private Boolean isRadialSearch;

    @Override
    public final boolean isInitialised() {
        return initialised;
    }

    @Override
    public final void destroy() throws Exception {
        if (executorService != null) {
            executorService.shutdown();
        }
    }

    @Override
    public final void init(HttpServletRequest req, HttpServletResponse resp, PageHandler method) {

        if (initialised) {
            throw new IllegalStateException("Already initialised");
        }

        GumtreePage page = method.getPageAnnotation();

        if (page == null) {
            throw new IllegalArgumentException("GumtreePage annotation must not be null");
        }

        pageType = page.value();
        executorService = executorServiceFactory.newFixedThreadPool(page.parallelisation());
        absoluteRequestUrl = buildAbsoluteUrl(req);
        referrer = req.getHeader("Referer");
        category = categoryService.getByUniqueName(Categories.ALL.getSeoName()).get();
        categoryLevelHierarchy = new HashMap<>();
        buildHomepageLocation();
        location = homepageLocation;
        deviceType = getDeviceType(req);

        initialised = true;
        request = req;
        response = resp;

        postInit(req, resp, method);
    }

    private String getDeviceType(HttpServletRequest req) {
        Device device = deviceResolver.resolveDevice(req);

        if (device.isMobile()) {
            return "desktop-mobile";
        }
        if (device.isTablet()) {
            return "desktop-tablet";
        }
        return "desktop-computer";
    }

    /**
     * Post common initialisation hook for extension.
     *
     * @param req    the request
     * @param resp   the response
     * @param method the page handler
     */
    public void postInit(HttpServletRequest req, HttpServletResponse resp, PageHandler method) {
        // Do nothing by default
    }

    @Override
    public final void populateModel(Map<String, Object> model, PageHandler method) {
        model.put("platform", getDeviceType());
        model.put("headerModel", pageModelFactory.createHeaderModel(this, method.getPageAnnotation()));
        model.put("footerModel", pageModelFactory.createFooterModel(this, method.getPageAnnotation()));
    }

    @Override
    public final PageType getPageType() {
        return pageType;
    }

    @Override
    public final void setPageType(PageType pageType) {
        this.pageType = pageType;
    }

    @Override
    public final Location getHomepageLocation() {
        return homepageLocation;
    }

    @Override
    public final Location getLocation() {
        return location;
    }

    @Override
    public final void setLocation(Location location) {
        this.location = location;
    }

    @Override
    public final LegacySite getLegacySite() {
        return legacySiteMap.getLegacySite(location.getName());
    }

    @Override
    public final Location getCounty() {
        return locationService.getCounty(location);
    }

    @Override
    public Map<Integer, Location> getLocationHierarchy() {
        return locationService.getLocationHierarchy(location);
    }

    @Override
    public final Category getCategory() {
        return category;
    }

    @Override
    public final void setCategory(Category category) {
        this.category = Preconditions.checkNotNull(category);
    }

    @Override
    public final void setCategoryLevelHierarchy(Map<Integer, Category> levelHierarchy) {
        this.categoryLevelHierarchy = levelHierarchy;
    }

    @Override
    public final Category getL1Category() {
        return categoryLevelHierarchy != null && categoryLevelHierarchy.containsKey(1)
                ? categoryLevelHierarchy.get(1)
                : null;
    }

    @Override
    public final Map<Integer, Category> getCategoryLevelHierarchy() {
        return categoryLevelHierarchy;
    }

    @Override
    public final URL getAbsoluteRequestUrl() {
        return absoluteRequestUrl;
    }

    @Override
    public final String getReferrer() {
        return referrer;
    }

    @Override
    public final HttpServletRequest getHttpServletRequest(){
        return request;
    }

    @Override
    public HttpServletResponse getHttpServletResponse() {
        return response;
    }

    @Override
    public final ExecutorService getExecutorService() {
        return executorService;
    }

    @Override
    public final Order getOrder() {
        return order;
    }

    @Override
    public void setOrder(Order order) {
        this.order = order;
    }

    @Override
    public final void setUser(User user) {
        this.user = user;
    }

    @Override
    public final User getUser() {
        return user;
    }

    @Override
    public final String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    @Override
    public final void setSearchTerm(String searchTerm) {
        this.searchTerm = HtmlUtils.htmlEscape(searchTerm);
    }

    @Override
    public final String getSearchTerm() {
        return searchTerm;
    }

    @Override
    public final void setHasContent(Boolean hasContent) {
        this.hasContent = hasContent;
    }

    @Override
    public final Boolean hasContent() {
        return hasContent;
    }

    @Override
    public void setDisplayAdsViewMode(DisplayAdsViewMode displayAdsViewMode) {
        this.displayAdsViewMode = displayAdsViewMode;
    }

    @Override
    public DisplayAdsViewMode getDisplayAdsViewMode() {
        return displayAdsViewMode;
    }

    @Override
    public void setPageNumber(Integer currentPageNumber) {
        this.currentPageNumber = currentPageNumber;
    }

    @Override
    public Integer getPageNumber() {
        return currentPageNumber;
    }

    @Override
    public final void setUrl(String url) {
        this.url = url;
    }

    @Override
    public final String getUrl() {
        return url;
    }

    @Override
    public void setAbTestTypes(List<AbTestType> abTestTypes) {
        this.abTestTypes = abTestTypes;
    }

    @Override
    public List<AbTestType> getAbTestTypes() {
        return abTestTypes;
    }

    public void setPageModel(T model) {
        this.model = model;
    }

    @Override
    public T getPageModel() {
        return model;
    }

    @Override
    public final String getLocationUserInput() {
        return userIputLocation;
    }

    @Override
    public final void setLocationUserInput(String userInputLocation) {
        this.userIputLocation = userInputLocation;
    }

    @Override
    public Boolean isRadialSearch() {
        return isRadialSearch;
    }

    @Override
    public void setRadialSearch(Boolean radialSearch) {
        isRadialSearch = radialSearch;
    }

    private void buildHomepageLocation() {
        homepageLocation = locationService.getRootLocation();
    }

    private URL buildAbsoluteUrl(HttpServletRequest request) {
        try {
            URL reconstructedURL;
            int port = request.getServerPort();
            String requestUri = request.getRequestURI();
            String queryString = request.getQueryString();
            if (queryString != null) {
                requestUri = StringUtils.concat(requestUri, "?", queryString);
            }
            if (port == 80) {
                reconstructedURL = new URL(request.getScheme(),
                        request.getServerName(),
                        requestUri);
            } else {
                reconstructedURL = new URL(request.getScheme(),
                        request.getServerName(),
                        request.getServerPort(),
                        requestUri);
            }

            return reconstructedURL;
        } catch (MalformedURLException ex) {
            throw new IllegalStateException(ex);
        }
    }

}
