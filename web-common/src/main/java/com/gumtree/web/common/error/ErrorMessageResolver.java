package com.gumtree.web.common.error;

/**
 * For resolving error message codes into messages.
 */
public interface ErrorMessageResolver {

    /**
     * Resolve message code into message.
     *
     * @param code           the message code
     * @param defaultMessage the fallback message
     * @param args           message args
     * @return a message for given message code
     */
    String getMessage(String code, String defaultMessage, Object... args);
}
