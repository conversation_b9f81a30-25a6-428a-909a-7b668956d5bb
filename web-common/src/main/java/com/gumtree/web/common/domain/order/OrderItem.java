package com.gumtree.web.common.domain.order;


import com.gumtree.api.Ad;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.domain.payment.ApiPaymentDetail;
import com.gumtree.seller.domain.order.status.OrderItemStatus;
import com.gumtree.seller.domain.product.entity.ProductName;

import java.math.BigDecimal;

/**
 *
 */
public interface OrderItem {

    /**
     * Return advert the item is associated with
     *
     * @return an advert
     */
    Ad getAdvert();

    /**
     * Return advert category
     *
     * @return an category
     */
    Category getCategory();

    /**
     * Return product the item is assoicated with
     *
     * @return the product name
     */
    ProductName getProductName();

    /**
     * Return the (potential) payment details the item has/may be paid for with
     *
     * @return payment details
     */
    ApiPaymentDetail getPaymentDetails();

    /**
     * Get the paid status of the order item
     *
     * @return the status
     */
    OrderItemStatus getStatus();

    /**
     * @return the price including VAT
     */
    BigDecimal getPriceIncVat();

    /**
     * @return true if item is free, false otherwise
     */
    Boolean isFree();
}
