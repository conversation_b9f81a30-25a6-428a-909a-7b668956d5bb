package com.gumtree.web.common.error.json;

import com.github.rjeschke.txtmark.Processor;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents a component error for JSON error responses.
 */
public final class JsonComponentError {

    private String component;

    private List<String> messages;

    /**
     * Constructor.
     *
     * @param component the component name
     * @param messages  the messages for this component
     */
    public JsonComponentError(String component, List<String> messages) {
        this.component = component;
        this.messages = messages;
    }

    public String getComponent() {
        return component;
    }

    public List<String> getMessages() {
        return messages;
    }

    /**
     * For building instances of {@link JsonComponentError}.
     */
    public static final class Builder {

        private String component = "";

        private List<String> messages = new ArrayList<String>();

        /**
         * @param component the component
         * @return Builder
         */
        public Builder withComponentName(String component) {
            this.component = component;
            return this;
        }

        /**
         * @param message the message
         * @return Builder with a message
         */
        public Builder withMessage(String message) {
            messages.add(Processor.process(message).trim());
            return this;
        }

        /**
         * @return JsonComponentError
         */
        public JsonComponentError build() {
            return new JsonComponentError(component, messages);
        }
    }

    /**
     * @return a {@link JsonComponentError} builder.
     */
    public static Builder aComponentError() {
        return new Builder();
    }
}
