package com.gumtree.web.common.sapi;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;

import java.util.List;

public enum AdvertFieldNames {
    SOURCE("_source"),
    IDS("_id"),
    ATTRIBUTE("attribute"),
    DISTANCE("distance"),
    LATITUDE("latitude"),
    LONGITUDE("longitude"),
    LOCATION("location"),
    ACCOUNT_ID("account.id"),
    LOCATION_ID("locations.id", "location_id"),
    LOCATION_NAME_SEARCH("locations.display_name.search"),
    LOCATION_NAME("locations.display_name"),
    GEO_LOCATION("pin.location"),
    USER_ENTERED_AREA("local_area"),
    CATEGORY_ID("categories.id", "category_id"),
    CATEGORY_NAME("categories.display_name"),
    CATEGORY_NAME_SEARCH("categories.display_name.search"),
    PUBLISHED_DATE("published_date"),
    EXPIRY_DATE("expiry_date"),
    ARCHIVED_DATE("archived_date"),
    DELETED_DATE("deleted_date"),
    STATUS("status"),
    FEATURES("features.product"),
    PRIMARY_IMAGE_URL("primary_image_url"),
    CREATED_DATE("created_date"),
    COOKIE("cookie"),
    IP_ADDRESS("ip_address"),
    LAST_MODIFIED_DATE("last_modified_date"),
    CONTACT_NAME("contact_name"),
    PAID_FOR("paid_for"),
    OUTCODE("outcode"),
    SEARCH("search"),
    TITLE_SEARCH("title.search"),
    DESCRIPTION_SEARCH("description.search"),
    ATTR_SEARCH_ALL("attribute_search"),
    ATTR_VEHICLE_MODEL("vehicle_model_search", AdvertAttributeNames.VEHICLE_MODEL.getName()),
    ATTR_PRICE(AdvertAttributeNames.PRICE),
    ATTR_SELLER_TYPE(AdvertAttributeNames.SELLER_TYPE),
    ATTR_VEHICLE_FUEL_TYPE(AdvertAttributeNames.VEHICLE_FUEL_TYPE),
    ATTR_VEHICLE_REGISTRATION_YEAR(AdvertAttributeNames.VEHICLE_REGISTRATION_YEAR),
    ATTR_VEHICLE_BODY_TYPE(AdvertAttributeNames.VEHICLE_BODY_TYPE),
    ATTR_VEHICLE_COLOUR(AdvertAttributeNames.VEHICLE_COLOUR),
    ATTR_VEHICLE_MILEAGE(AdvertAttributeNames.VEHICLE_MILEAGE),
    ATTR_VEHICLE_TRANSMISSION(AdvertAttributeNames.VEHICLE_TRANSMISSION),
    ATTR_VEHICLE_MAKE(AdvertAttributeNames.VEHICLE_MAKE),
    ATTR_VEHICLE_ENGINE_SIZE(AdvertAttributeNames.VEHICLE_ENGINE_SIZE),
    ATTR_MOTORBIKE_MAKE(AdvertAttributeNames.MOTORBIKE_MAKE),
    ATTR_JOB_CONTRACT_TYPE(AdvertAttributeNames.JOB_CONTRACT_TYPE),
    ATTR_PROPERTY_TYPE(AdvertAttributeNames.PROPERTY_TYPE),
    ATTR_PROPERTY_COUPLES(AdvertAttributeNames.PROPERTY_COUPLES),
    ATTR_PROPERTY_ROOM_TYPE(AdvertAttributeNames.PROPERTY_ROOM_TYPE),
    ATTR_PROPERTY_PRICE_FREQUENCY(AdvertAttributeNames.PRICE_FREQUENCY),
    PROPERTY_NUMBER_BEDS(AdvertAttributeNames.PROPERTY_NUMBER_BEDS),
    EVENT_DATE_ATTRIBUTE(AdvertAttributeNames.EVENT_DATE),
    TRAVEL_DATE_ATTRIBUTE(AdvertAttributeNames.TRAVEL_DATE),
    LAWN_MOWER_TYPE(AdvertAttributeNames.LAWN_MOWER_TYPE),
    DELETE_REASON("delete_reason"),
    IMAGE_ONLY(PRIMARY_IMAGE_URL.getName(), "images_only"),
    URGENT_ONLY(FEATURES.getName(), "urgent_only", Lists.newArrayList(new String[]{"URGENT"})),
    FEATURED_ONLY(FEATURES.getName(), "featured_only",
            Lists.newArrayList(new String[]{"FEATURE_3_DAY", "FEATURE_7_DAY", "FEATURE_14_DAY"})),
    VERIFIED_PHONE("created_by.verified_phone_number"),
    ACCOUNT_PROFESSIONAL("account.pro"),
    ACCOUNT_POSTING_SINCE("account.posting_since");

    private String name;
    private String clientKey;
    private Optional<List<String>> fixedValues;

    private AdvertFieldNames(String name, String clientKey, List<String> fixedValues) {
        this.name = name;
        this.clientKey = clientKey;
        if (fixedValues == null) {
            this.fixedValues = Optional.absent();
        } else {
            this.fixedValues = Optional.of(fixedValues);
        }

    }

    private AdvertFieldNames(String name, String clientKey) {
        this(name, clientKey, (List)null);
    }

    private AdvertFieldNames(String name) {
        this(name, name);
    }

    private AdvertFieldNames(AdvertAttributeNames attribute) {
        this(attributeName(attribute), attribute.getName());
    }

    public String getName() {
        return this.name;
    }

    public List<String> getFieldValues() {
        return (List)this.fixedValues.get();
    }

    public static String attributeName(AdvertAttributeNames name) {
        return ATTRIBUTE.getName() + "." + name.getName();
    }

    public static String attributeName(String name) {
        return ATTRIBUTE.getName() + "." + name;
    }
}

