package com.gumtree.web.common.domain.messagecentre;

public class PollingFrequencies {
    private final Integer pollingVisibleInterval;
    private final Integer pollingHiddenInterval;

    public PollingFrequencies(Integer pollingVisibleInterval, Integer pollingHiddenInterval) {
        this.pollingVisibleInterval = pollingVisibleInterval;
        this.pollingHiddenInterval = pollingHiddenInterval;
    }

    public Integer getPollingVisibleInterval() {
        return pollingVisibleInterval;
    }

    public Integer getPollingHiddenInterval() {
        return pollingHiddenInterval;
    }

    @Override
    public String toString() {
        return "PollingFrequencies{" +
                "pollingVisibleInterval=" + pollingVisibleInterval +
                ", pollingHiddenInterval=" + pollingHiddenInterval +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PollingFrequencies)) return false;

        PollingFrequencies that = (PollingFrequencies) o;

        if (pollingVisibleInterval != null
                ? !pollingVisibleInterval.equals(that.pollingVisibleInterval) : that.pollingVisibleInterval != null) {
            return false;
        }
        return pollingHiddenInterval != null
                ? pollingHiddenInterval.equals(that.pollingHiddenInterval) : that.pollingHiddenInterval == null;
    }

    @Override
    public int hashCode() {
        int result = pollingVisibleInterval != null ? pollingVisibleInterval.hashCode() : 0;
        result = 31 * result + (pollingHiddenInterval != null ? pollingHiddenInterval.hashCode() : 0);
        return result;
    }
}
