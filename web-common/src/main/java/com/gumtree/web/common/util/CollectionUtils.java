package com.gumtree.web.common.util;

import com.google.common.base.Function;
import com.google.common.base.Predicate;
import com.google.common.collect.Iterables;

import java.util.ArrayList;
import java.util.List;

/**
 */
public final class CollectionUtils {

    /**
     * Utility class constructor.
     */
    private CollectionUtils() {

    }

    /**
     * Filter a list using a predicate
     *
     * @param unfiltered the unfiltered list
     * @param p          the predicate
     * @param <T>        the list item type
     * @return a filtered version of the input list
     */
    public static <T> List<T> filter(List<T> unfiltered, Predicate<T> p) {
        List<T> filtered = new ArrayList<>();
        Iterables.addAll(filtered, Iterables.filter(unfiltered, p));
        return filtered;
    }

    /**
     * Transform a list of items.
     *
     * @param source the source list
     * @param f      the function to transform items
     * @param <S>    the source type
     * @param <T>    the target type
     * @return transformed list
     */
    public static <S, T> List<T> transform(List<S> source, Function<S, T> f) {
        List<T> transformed = new ArrayList<>();
        Iterables.addAll(transformed, Iterables.transform(source, f));
        return transformed;
    }

    /**
     * Creates new list from given list by taking n elements. Size of returned list will be no greater than n: min(size of list, n).
     *
     * @param list list
     * @param n    number of elements to be taken from list
     * @param <S>
     * @return sublist
     */
    public static <S> List<S> take(List<S> list, int n) {
        if (list == null) {
            return null;
        }
        final int elements = Math.min(list.size(), n);
        return list.subList(0, elements);
    }
}
