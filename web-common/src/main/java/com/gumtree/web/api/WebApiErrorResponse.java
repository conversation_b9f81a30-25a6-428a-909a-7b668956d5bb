package com.gumtree.web.api;

import org.springframework.http.HttpStatus;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;

public class WebApiErrorResponse {
    private final HttpStatus status;
    private final String code;
    private final String message;
    private final List<WebApiError> errors;

    public WebApiErrorResponse(HttpStatus status, String code, String message, List<WebApiError> errors) {
        Assert.notNull(status);
        Assert.notNull(code);
        Assert.notNull(message);
        Assert.notNull(errors);

        this.status = status;
        this.code = code;
        this.message = message;
        this.errors = errors;
    }

    public WebApiErrorResponse(HttpStatus status, String code, String message) {
        Assert.notNull(status);
        Assert.notNull(code);
        Assert.notNull(message);

        this.status = status;
        this.code = code;
        this.message = message;
        this.errors = Collections.emptyList();
    }

    public HttpStatus getStatus() {
        return status;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public List<WebApiError> getErrors() {
        return errors;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof WebApiErrorResponse)) return false;

        WebApiErrorResponse that = (WebApiErrorResponse) o;

        if (status != that.status) return false;
        if (code != null ? !code.equals(that.code) : that.code != null) return false;
        if (message != null ? !message.equals(that.message) : that.message != null) return false;
        return errors != null ? errors.equals(that.errors) : that.errors == null;
    }

    @Override
    public int hashCode() {
        int result = status != null ? status.hashCode() : 0;
        result = 31 * result + (code != null ? code.hashCode() : 0);
        result = 31 * result + (message != null ? message.hashCode() : 0);
        result = 31 * result + (errors != null ? errors.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "WebApiErrorResponse{" +
                "status=" + status +
                ", code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", errors=" + errors +
                '}';
    }
}
