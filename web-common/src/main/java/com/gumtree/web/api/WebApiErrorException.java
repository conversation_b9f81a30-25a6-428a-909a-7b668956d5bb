package com.gumtree.web.api;

import org.springframework.util.Assert;

import javax.annotation.Nonnull;

public class WebApiErrorException extends Exception {

    public enum LogLevel {
        DEBUG,
        INFO,
        WARN,
        ERROR,
        OFF
    }

    private final WebApiErrorResponse error;

    private final LogLevel logLevel;

    public WebApiErrorException(@Nonnull WebApiErrorResponse error) {
        this(error, LogLevel.OFF);
    }

    public WebApiErrorException(@Nonnull WebApiErrorResponse error, LogLevel logLevel) {
        super("Code : " + error.getCode() + "; Message : " + error.getMessage());
        Assert.notNull(error, "Error is required");

        this.error = error;
        this.logLevel = logLevel;
    }

    public WebApiErrorResponse getError() {
        return error;
    }

    public LogLevel getLogLevel() {
        return logLevel;
    }
}
