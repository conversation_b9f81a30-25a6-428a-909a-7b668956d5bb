package com.gumtree.seo;

import com.gumtree.common.util.io.monitor.FileChangeListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.io.FileSystemResource;

import java.io.File;
import java.io.IOException;

public class CategorySeoConfigFileChangeListener extends FileChangeListener implements ApplicationContextAware {
    private static final Logger LOG = LoggerFactory.getLogger(CategorySeoConfigFileChangeListener.class);

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public void onFileChange(File file) {
        CategorySeoConfig categoryConfig;
        try {
            LOG.debug("Category config file change detected, going to reload the file. File: " + file);
            categoryConfig = new YamlCategorySeoConfigFactory(new FileSystemResource(file)).getNewInstance();
            applicationContext.publishEvent(new CategoryConfigModifiedEvent(this, categoryConfig));
        } catch (IOException e) {
            LOG.error(String.format("Failed to reload category config file '%s'", file), e);
        }
    }
}
