package com.gumtree.web.cookie;

import java.util.HashMap;
import java.util.Map;

public abstract class MultiValueCookie extends BaseCookie<Map<String, String>> {

    protected MultiValueCookie(String domain, int maxAge, String path, Boolean httpOnly, String value) {
        super(domain, maxAge, path, httpOnly, CookieSerializer.deserializeCookieMap(value));
    }

    protected MultiValueCookie(String domain, int maxAge, String path, Boolean httpOnly, Map<String,String> entries) {
        super(domain, maxAge, path, httpOnly, entries);
    }

    protected MultiValueCookie(String domain, int maxAge, String path, Boolean httpOnly) {
        super(domain, maxAge, path, httpOnly);
    }

    public abstract String getName();

    protected String get(String key) {
        return getValue().get(key);
    }

    protected void put(String key, String value) {
        getValue().put(key, value);
    }

    protected void remove(String key) {
        getValue().remove(key);
    }

    @Override
    protected Map<String, String> getDefaultValue() {
        return new HashMap<String, String>();
    }

    @Override
    protected String getValueAsString() {
        return CookieSerializer.serializeCookieMap(getValue());
    }
}
