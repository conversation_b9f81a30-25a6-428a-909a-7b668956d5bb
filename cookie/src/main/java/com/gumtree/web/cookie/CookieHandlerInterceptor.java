package com.gumtree.web.cookie;

import com.gumtree.common.properties.GtProps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class CookieHandlerInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private CookieCutter[] cookieCutters;

    @Override
    public void postHandle(HttpServletRequest request,
                           HttpServletResponse response,
                           Object handler,
                           ModelAndView modelAndView) {
        for (CookieCutter cookieCutter : cookieCutters) {
            String cookieName = cookieCutter.getName(GtProps.getEnv());
            BaseCookie baseCookie = (BaseCookie) request.getAttribute(cookieName);
            if (baseCookie != null) {
                Cookie cookie = CookieUtils.createHttpCookie(cookieName, baseCookie);
                response.addCookie(cookie);
            }
        }
    }
}
