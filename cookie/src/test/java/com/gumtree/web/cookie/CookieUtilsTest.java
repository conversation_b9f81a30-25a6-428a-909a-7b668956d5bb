package com.gumtree.web.cookie;

import org.junit.Test;

import javax.servlet.http.Cookie;
import java.util.Optional;

import static org.fest.assertions.api.Assertions.assertThat;

public class CookieUtilsTest {

    @Test
    public void shouldNotFindCookie() {
        assertThat(CookieUtils.findCookie(null, "cookie-name")).isEqualTo(Optional.<Cookie>empty());
        assertThat(CookieUtils.findCookie(new Cookie[]{}, "cookie-name")).isEqualTo(Optional.<Cookie>empty());
        assertThat(CookieUtils.findCookie(new Cookie[]{new Cookie("other", "xxx")}, "cookie-name")).isEqualTo(Optional.<Cookie>empty());
    }

    @Test
    public void shouldFindCookieByName() {
        // given
        Cookie cookie = new Cookie("wanted", "xxx");
        Cookie otherCookie = new Cookie("other", "xxx");
        Cookie[] cookies = {cookie, otherCookie};

        // when
        Optional<Cookie> actual = CookieUtils.findCookie(cookies, "wanted");

        // then
        assertThat(actual).isEqualTo(Optional.of(cookie));
    }
}
